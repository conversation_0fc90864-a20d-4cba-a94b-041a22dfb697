"""
GenAI Entry Detection Utilities
===============================

Common utilities for detecting and marking GenAI entry spans across all
GenAI instrumentation packages.
"""

import os
import logging
from functools import wraps
from opentelemetry import trace

logger = logging.getLogger(__name__)

# Environment variable for controlling GenAI entry marking
GENAI_ENTRY_ENV_VAR = "OTEL_MARK_GENAI_ENTRY"

# Span attribute name for marking GenAI entry spans
GENAI_ENTRY_ATTRIBUTE = "is_genai_entry"


def is_genai_entry_enabled() -> bool:
    """
    Check if GenAI entry marking is enabled via environment variable.

    Returns:
        bool: True if enabled (default), False if disabled
    """
    return os.getenv(GENAI_ENTRY_ENV_VAR, "true").lower() == "true"


def mark_span_as_genai_entry(span) -> None:
    """
    Mark a span as GenAI entry point.

    Args:
        span: The OpenTelemetry span to mark
    """
    try:
        if span and span.is_recording():
            span.set_attribute(GENAI_ENTRY_ATTRIBUTE, True)
    except Exception as e:
        logger.warning(f"Failed to mark span as GenAI entry: {e}")


def with_genai_entry_detection(wrapper_func):
    """
    Decorator that adds GenAI entry detection to wrapper functions.

    This decorator works by intercepting the span creation and adding
    entry detection immediately after the span is created.

    Args:
        wrapper_func: The wrapper function to enhance

    Returns:
        Enhanced wrapper function with GenAI entry detection
    """

    @wraps(wrapper_func)
    def enhanced_wrapper(*args, **kwargs):
        # Call the original wrapper function
        result = wrapper_func(*args, **kwargs)

        # Try to add GenAI entry detection to the current span
        try:
            if is_genai_entry_enabled():
                current_span = trace.get_current_span()
                if current_span != trace.INVALID_SPAN:
                    mark_span_as_genai_entry(current_span)
        except Exception as e:
            logger.warning(f"Failed to add GenAI entry detection: {e}")

        return result

    return enhanced_wrapper
