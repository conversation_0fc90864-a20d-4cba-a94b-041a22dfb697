"""
GenAI Entry Detection Utilities
===============================

Common utilities for detecting and marking GenAI entry spans across all
GenAI instrumentation packages.
"""

import os
import logging
from functools import wraps
from opentelemetry import trace

logger = logging.getLogger(__name__)

# Environment variable for controlling GenAI entry marking
GENAI_ENTRY_ENV_VAR = "OTEL_MARK_GENAI_ENTRY"

# Span attribute name for marking GenAI entry spans
GENAI_ENTRY_ATTRIBUTE = "is_genai_entry"


def is_genai_entry_enabled() -> bool:
    """
    Check if GenAI entry marking is enabled via environment variable.

    Returns:
        bool: True if enabled (default), False if disabled
    """
    return os.getenv(GENAI_ENTRY_ENV_VAR, "true").lower() == "true"


def mark_span_as_genai_entry(span) -> None:
    """
    Mark a span as GenAI entry point.

    Args:
        span: The OpenTelemetry span to mark
    """
    try:
        if span and span.is_recording():
            span.set_attribute(GENAI_ENTRY_ATTRIBUTE, True)
    except Exception as e:
        logger.warning(f"Failed to mark span as GenAI entry: {e}")


def with_genai_entry_detection(wrapper_func):
    """
    Decorator that adds GenAI entry detection to wrapper functions.

    This decorator works by intercepting the wrapper function and adding
    entry detection logic that marks the span during execution.

    Args:
        wrapper_func: The wrapper function to enhance

    Returns:
        Enhanced wrapper function with GenAI entry detection
    """
    import asyncio

    if asyncio.iscoroutinefunction(wrapper_func):
        @wraps(wrapper_func)
        async def enhanced_async_wrapper(*args, **kwargs):
            # Store the original span creation to intercept it
            original_start_span = None
            if len(args) > 0 and hasattr(args[0], 'start_span'):
                tracer = args[0]
                original_start_span = tracer.start_span

                def enhanced_start_span(*span_args, **span_kwargs):
                    span = original_start_span(*span_args, **span_kwargs)
                    # Mark the span as GenAI entry if enabled
                    if is_genai_entry_enabled():
                        mark_span_as_genai_entry(span)
                    return span

                tracer.start_span = enhanced_start_span

            try:
                # Call the original wrapper function
                result = await wrapper_func(*args, **kwargs)
            finally:
                # Restore original start_span method
                if original_start_span is not None:
                    tracer.start_span = original_start_span

            return result

        return enhanced_async_wrapper
    else:
        @wraps(wrapper_func)
        def enhanced_wrapper(*args, **kwargs):
            # Store the original span creation to intercept it
            original_start_span = None
            if len(args) > 0 and hasattr(args[0], 'start_span'):
                tracer = args[0]
                original_start_span = tracer.start_span

                def enhanced_start_span(*span_args, **span_kwargs):
                    span = original_start_span(*span_args, **span_kwargs)
                    # Mark the span as GenAI entry if enabled
                    if is_genai_entry_enabled():
                        mark_span_as_genai_entry(span)
                    return span

                tracer.start_span = enhanced_start_span

            try:
                # Call the original wrapper function
                result = wrapper_func(*args, **kwargs)
            finally:
                # Restore original start_span method
                if original_start_span is not None:
                    tracer.start_span = original_start_span

            return result

        return enhanced_wrapper
