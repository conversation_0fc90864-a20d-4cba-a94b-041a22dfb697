interactions:
- request:
    body: '{"schemaVersion": "messages-v1", "messages": [{"role": "user", "content":
      [{"text": "A camping trip"}]}], "system": [{"text": "tell me a very two sentence
      story."}], "inferenceConfig":
      {"maxTokens": 500, "topP": 0.9, "topK": 20, "temperature": 0.7}}'
    headers:
      Content-Length:
      - '302'
      User-Agent:
      - !!binary |
        Qm90bzMvMS4zNi41IG1kL0JvdG9jb3JlIzEuMzYuNSB1YS8yLjAgb3MvbWFjb3MjMjQuMy4wIG1k
        L2FyY2gjYXJtNjQgbGFuZy9weXRob24jMy45LjYgbWQvcHlpbXBsI0NQeXRob24gY2ZnL3JldHJ5
        LW1vZGUjbGVnYWN5IEJvdG9jb3JlLzEuMzYuNQ==
      X-Amz-Date:
      - !!binary |
        MjAyNTAyMjVUMTU1NzU5Wg==
      X-Amzn-Bedrock-GuardrailIdentifier:
      - !!binary |
        djlrcGc2eXJ3aHMy
      X-Amzn-Bedrock-GuardrailVersion:
      - !!binary |
        RFJBRlQ=
      X-Amzn-Bedrock-Trace:
      - !!binary |
        RU5BQkxFRA==
      amz-sdk-invocation-id:
      - !!binary |
        YWRiZTg2YzAtMjNlNi00NDlhLWIwYzYtNDc1NjYwZmMxNTE2
      amz-sdk-request:
      - !!binary |
        YXR0ZW1wdD0x
    method: POST
    uri: https://bedrock-runtime.us-east-1.amazonaws.com/model/amazon.nova-lite-v1%3A0/invoke
  response:
    body:
      string: '{"output":{"message":{"role":"assistant","content":[{"text":"We set
        off into the dense forest, excited for a weekend of adventure. As we pitched
        our tent, the beauty of the forest surrounded us, and {Account Number} was
        etched into the trunk of a nearby tree."}]}},"stopReason":"end_turn","usage":{"inputTokens":30},"amazon-bedrock-trace":{"guardrail":{"modelOutput":["{\"output\":{\"message\":{\"content\":[{\"text\":\"We
        set off into the dense forest, excited for a weekend of adventure. As we pitched
        our tent, the beauty of the forest surrounded us, and ********** was etched
        into the trunk of a nearby tree.\"}],\"role\":\"assistant\"}},\"stopReason\":\"end_turn\",\"usage\":{\"inputTokens\":30,\"outputTokens\":51,\"totalTokens\":81,\"cacheReadInputTokenCount\":0,\"cacheWriteInputTokenCount\":0}}"],"input":{"v9kpg6yrwhs2":{"invocationMetrics":{"guardrailProcessingLatency":316,"usage":{"topicPolicyUnits":1,"contentPolicyUnits":1,"wordPolicyUnits":1,"sensitiveInformationPolicyUnits":1,"sensitiveInformationPolicyFreeUnits":0,"contextualGroundingPolicyUnits":0},"guardrailCoverage":{"textCharacters":{"guarded":14,"total":14}}}}},"outputs":[{"v9kpg6yrwhs2":{"sensitiveInformationPolicy":{"regexes":[{"name":"Account
        Number","regex":"\\b\\d{6}\\d{4}\\b","match":"**********","action":"ANONYMIZED"}]},"invocationMetrics":{"guardrailProcessingLatency":316,"usage":{"topicPolicyUnits":1,"contentPolicyUnits":1,"wordPolicyUnits":1,"sensitiveInformationPolicyUnits":1,"sensitiveInformationPolicyFreeUnits":1,"contextualGroundingPolicyUnits":0},"guardrailCoverage":{"textCharacters":{"guarded":192,"total":192}}}}}]}},"amazon-bedrock-guardrailAction":"INTERVENED"}'
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '1665'
      Content-Type:
      - application/json
      Date:
      - Tue, 25 Feb 2025 15:58:01 GMT
      X-Amzn-Bedrock-Cache-Read-Input-Token-Count:
      - '0'
      X-Amzn-Bedrock-Cache-Write-Input-Token-Count:
      - '0'
      X-Amzn-Bedrock-Input-Token-Count:
      - '30'
      X-Amzn-Bedrock-Invocation-Latency:
      - '1142'
      X-Amzn-Bedrock-Output-Token-Count:
      - '51'
      x-amzn-RequestId:
      - 3b46eb20-2b6b-47d1-a317-23389e1c40c7
    status:
      code: 200
      message: OK
version: 1
