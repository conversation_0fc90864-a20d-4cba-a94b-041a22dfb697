interactions:
- request:
    body: '{"messages": [{"role": "user", "content": [{"text": "Tell me a joke about
      OpenTelemetry"}]}], "inferenceConfig": {"maxTokens": 500, "topP": 0.9, "topK":
      20, "temperature": 0.7}}'
    headers:
      Content-Length:
      - '177'
      User-Agent:
      - !!binary |
        Qm90bzMvMS4zNy4xMyBtZC9Cb3RvY29yZSMxLjM3LjEzIHVhLzIuMSBvcy9tYWNvcyMyNC4zLjAg
        bWQvYXJjaCNhcm02NCBsYW5nL3B5dGhvbiMzLjkuNiBtZC9weWltcGwjQ1B5dGhvbiBjZmcvcmV0
        cnktbW9kZSNsZWdhY3kgQm90b2NvcmUvMS4zNy4xMw==
      X-Amz-Date:
      - !!binary |
        MjAyNTAzMTdUMTQxOTIyWg==
      amz-sdk-invocation-id:
      - !!binary |
        MTQ1ZTJiMTktYzRiMC00OTQzLTkxNzEtNjdiZjA3NDRhYmFj
      amz-sdk-request:
      - !!binary |
        YXR0ZW1wdD0x
    method: POST
    uri: https://bedrock-runtime.us-east-1.amazonaws.com/model/us.amazon.nova-lite-v1%3A0/invoke
  response:
    body:
      string: '{"output":{"message":{"content":[{"text":"Sure, here''s a joke for
        you:\n\nWhy did the OpenTelemetry developer bring a ladder to work?\n\nBecause
        they heard the company was going to great lengths to trace their performance
        issues to the \"root cause\"!\n\nHope that brought a smile to your face!"}],"role":"assistant"}},"stopReason":"end_turn","usage":{"inputTokens":7,"outputTokens":53,"totalTokens":60,"cacheReadInputTokenCount":0,"cacheWriteInputTokenCount":0}}'
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '463'
      Content-Type:
      - application/json
      Date:
      - Mon, 17 Mar 2025 14:19:23 GMT
      X-Amzn-Bedrock-Cache-Read-Input-Token-Count:
      - '0'
      X-Amzn-Bedrock-Cache-Write-Input-Token-Count:
      - '0'
      X-Amzn-Bedrock-Input-Token-Count:
      - '7'
      X-Amzn-Bedrock-Invocation-Latency:
      - '596'
      X-Amzn-Bedrock-Output-Token-Count:
      - '53'
      x-amzn-RequestId:
      - ae14e85a-2f22-46b0-8540-277b810fab77
    status:
      code: 200
      message: OK
version: 1
