interactions:
- request:
    body: '{"prompt": "Translate to spanish: ''Amazon Bedrock is the easiest way to
      build andscale generative AI applications with base models (FMs)''.", "maxTokens":
      200, "temperature": 0.5, "topP": 0.5}'
    headers:
      Accept:
      - !!binary |
        YXBwbGljYXRpb24vanNvbg==
      Content-Length:
      - '191'
      Content-Type:
      - !!binary |
        YXBwbGljYXRpb24vanNvbg==
      User-Agent:
      - !!binary |
        Qm90bzMvMS4zNC4xMjEgbWQvQm90b2NvcmUjMS4zNC4xMjEgdWEvMi4wIG9zL3dpbmRvd3MjMTAg
        bWQvYXJjaCNhbWQ2NCBsYW5nL3B5dGhvbiMzLjExLjUgbWQvcHlpbXBsI0NQeXRob24gY2ZnL3Jl
        dHJ5LW1vZGUjbGVnYWN5IEJvdG9jb3JlLzEuMzQuMTIx
      X-Amz-Date:
      - !!binary |
        MjAyNDA2MTFUMDAzNDMzWg==
      amz-sdk-invocation-id:
      - !!binary |
        NDg5ZmU0YTMtZWNmZS00ZGZjLTk2MmEtYjY0MTJiZmM4YmRk
      amz-sdk-request:
      - !!binary |
        YXR0ZW1wdD0x
    method: POST
    uri: https://bedrock-runtime.us-east-1.amazonaws.com/model/ai21.j2-mid-v1/invoke
  response:
    body:
      string: "{\"id\":1234,\"prompt\":{\"text\":\"Translate to spanish: 'Amazon Bedrock
        is the easiest way to build andscale generative AI applications with base
        models (FMs)'.\",\"tokens\":[{\"generatedToken\":{\"token\":\"\u2581\",\"logprob\":-3.0160698890686035,\"raw_logprob\":-3.0160698890686035},\"topTokens\":null,\"textRange\":{\"start\":0,\"end\":0}},{\"generatedToken\":{\"token\":\"Translate\",\"logprob\":-8.331533432006836,\"raw_logprob\":-8.331533432006836},\"topTokens\":null,\"textRange\":{\"start\":0,\"end\":9}},{\"generatedToken\":{\"token\":\"\u2581to\",\"logprob\":-2.9710845947265625,\"raw_logprob\":-2.9710845947265625},\"topTokens\":null,\"textRange\":{\"start\":9,\"end\":12}},{\"generatedToken\":{\"token\":\"\u2581spanish\",\"logprob\":-9.67746639251709,\"raw_logprob\":-9.67746639251709},\"topTokens\":null,\"textRange\":{\"start\":12,\"end\":20}},{\"generatedToken\":{\"token\":\":\",\"logprob\":-13.539094924926758,\"raw_logprob\":-13.539094924926758},\"topTokens\":null,\"textRange\":{\"start\":20,\"end\":21}},{\"generatedToken\":{\"token\":\"\u2581'\",\"logprob\":-15.360382080078125,\"raw_logprob\":-15.360382080078125},\"topTokens\":null,\"textRange\":{\"start\":21,\"end\":23}},{\"generatedToken\":{\"token\":\"Amazon\",\"logprob\":-10.461552619934082,\"raw_logprob\":-10.461552619934082},\"topTokens\":null,\"textRange\":{\"start\":23,\"end\":29}},{\"generatedToken\":{\"token\":\"\u2581Bed\",\"logprob\":-14.884922981262207,\"raw_logprob\":-14.884922981262207},\"topTokens\":null,\"textRange\":{\"start\":29,\"end\":33}},{\"generatedToken\":{\"token\":\"rock\",\"logprob\":-8.739396095275879,\"raw_logprob\":-8.739396095275879},\"topTokens\":null,\"textRange\":{\"start\":33,\"end\":37}},{\"generatedToken\":{\"token\":\"\u2581is\u2581the\u2581easiest\u2581way\",\"logprob\":-16.61186981201172,\"raw_logprob\":-16.61186981201172},\"topTokens\":null,\"textRange\":{\"start\":37,\"end\":56}},{\"generatedToken\":{\"token\":\"\u2581to\u2581build\",\"logprob\":-3.199646472930908,\"raw_logprob\":-3.199646472930908},\"topTokens\":null,\"textRange\":{\"start\":56,\"end\":65}},{\"generatedToken\":{\"token\":\"\u2581and\",\"logprob\":-4.354443073272705,\"raw_logprob\":-4.354443073272705},\"topTokens\":null,\"textRange\":{\"start\":65,\"end\":69}},{\"generatedToken\":{\"token\":\"scale\",\"logprob\":-11.503482818603516,\"raw_logprob\":-11.503482818603516},\"topTokens\":null,\"textRange\":{\"start\":69,\"end\":74}},{\"generatedToken\":{\"token\":\"\u2581\",\"logprob\":-7.607464790344238,\"raw_logprob\":-7.607464790344238},\"topTokens\":null,\"textRange\":{\"start\":74,\"end\":75}},{\"generatedToken\":{\"token\":\"generative\",\"logprob\":-15.344130516052246,\"raw_logprob\":-15.344130516052246},\"topTokens\":null,\"textRange\":{\"start\":75,\"end\":85}},{\"generatedToken\":{\"token\":\"\u2581AI\",\"logprob\":-2.475620746612549,\"raw_logprob\":-2.475620746612549},\"topTokens\":null,\"textRange\":{\"start\":85,\"end\":88}},{\"generatedToken\":{\"token\":\"\u2581applications\",\"logprob\":-3.508157968521118,\"raw_logprob\":-3.508157968521118},\"topTokens\":null,\"textRange\":{\"start\":88,\"end\":101}},{\"generatedToken\":{\"token\":\"\u2581with\",\"logprob\":-5.4546332359313965,\"raw_logprob\":-5.4546332359313965},\"topTokens\":null,\"textRange\":{\"start\":101,\"end\":106}},{\"generatedToken\":{\"token\":\"\u2581base\",\"logprob\":-13.933234214782715,\"raw_logprob\":-13.933234214782715},\"topTokens\":null,\"textRange\":{\"start\":106,\"end\":111}},{\"generatedToken\":{\"token\":\"\u2581models\",\"logprob\":-4.964498996734619,\"raw_logprob\":-4.964498996734619},\"topTokens\":null,\"textRange\":{\"start\":111,\"end\":118}},{\"generatedToken\":{\"token\":\"\u2581\",\"logprob\":-7.693998336791992,\"raw_logprob\":-7.693998336791992},\"topTokens\":null,\"textRange\":{\"start\":118,\"end\":119}},{\"generatedToken\":{\"token\":\"(\",\"logprob\":-6.192896366119385,\"raw_logprob\":-6.192896366119385},\"topTokens\":null,\"textRange\":{\"start\":119,\"end\":120}},{\"generatedToken\":{\"token\":\"FM\",\"logprob\":-11.4367094039917,\"raw_logprob\":-11.4367094039917},\"topTokens\":null,\"textRange\":{\"start\":120,\"end\":122}},{\"generatedToken\":{\"token\":\"s\",\"logprob\":-8.579822540283203,\"raw_logprob\":-8.579822540283203},\"topTokens\":null,\"textRange\":{\"start\":122,\"end\":123}},{\"generatedToken\":{\"token\":\")\",\"logprob\":-0.7525036334991455,\"raw_logprob\":-0.7525036334991455},\"topTokens\":null,\"textRange\":{\"start\":123,\"end\":124}},{\"generatedToken\":{\"token\":\"'.\",\"logprob\":-9.196444511413574,\"raw_logprob\":-9.196444511413574},\"topTokens\":null,\"textRange\":{\"start\":124,\"end\":126}}]},\"completions\":[{\"data\":{\"text\":\"\\n'Amazon
        Bedrock es la manera m\xE1s f\xE1cil de construir y escalado de aplicaciones
        generatives de AI con modelos base (FMs)'.\",\"tokens\":[{\"generatedToken\":{\"token\":\"<|newline|>\",\"logprob\":0.0,\"raw_logprob\":-3.6954811548639555E-6},\"topTokens\":null,\"textRange\":{\"start\":0,\"end\":1}},{\"generatedToken\":{\"token\":\"\u2581'\",\"logprob\":0.0,\"raw_logprob\":-0.05112015828490257},\"topTokens\":null,\"textRange\":{\"start\":1,\"end\":2}},{\"generatedToken\":{\"token\":\"Amazon\",\"logprob\":0.0,\"raw_logprob\":-0.002133119385689497},\"topTokens\":null,\"textRange\":{\"start\":2,\"end\":8}},{\"generatedToken\":{\"token\":\"\u2581Bed\",\"logprob\":0.0,\"raw_logprob\":-1.4232576359063387E-4},\"topTokens\":null,\"textRange\":{\"start\":8,\"end\":12}},{\"generatedToken\":{\"token\":\"rock\",\"logprob\":0.0,\"raw_logprob\":-8.189899963326752E-4},\"topTokens\":null,\"textRange\":{\"start\":12,\"end\":16}},{\"generatedToken\":{\"token\":\"\u2581\",\"logprob\":0.0,\"raw_logprob\":-0.02208741195499897},\"topTokens\":null,\"textRange\":{\"start\":16,\"end\":17}},{\"generatedToken\":{\"token\":\"es\",\"logprob\":0.0,\"raw_logprob\":-0.0015725638950243592},\"topTokens\":null,\"textRange\":{\"start\":17,\"end\":19}},{\"generatedToken\":{\"token\":\"\u2581la\",\"logprob\":0.0,\"raw_logprob\":-0.08937965333461761},\"topTokens\":null,\"textRange\":{\"start\":19,\"end\":22}},{\"generatedToken\":{\"token\":\"\u2581man\",\"logprob\":0.0,\"raw_logprob\":-0.2883087396621704},\"topTokens\":null,\"textRange\":{\"start\":22,\"end\":26}},{\"generatedToken\":{\"token\":\"era\",\"logprob\":0.0,\"raw_logprob\":-0.0012638922780752182},\"topTokens\":null,\"textRange\":{\"start\":26,\"end\":29}},{\"generatedToken\":{\"token\":\"\u2581m\xE1s\",\"logprob\":0.0,\"raw_logprob\":-0.003933788277208805},\"topTokens\":null,\"textRange\":{\"start\":29,\"end\":33}},{\"generatedToken\":{\"token\":\"\u2581f\",\"logprob\":0.0,\"raw_logprob\":-0.08932819217443466},\"topTokens\":null,\"textRange\":{\"start\":33,\"end\":35}},{\"generatedToken\":{\"token\":\"\xE1c\",\"logprob\":0.0,\"raw_logprob\":-6.794906312279636E-6},\"topTokens\":null,\"textRange\":{\"start\":35,\"end\":37}},{\"generatedToken\":{\"token\":\"il\",\"logprob\":0.0,\"raw_logprob\":-2.8609820219571702E-5},\"topTokens\":null,\"textRange\":{\"start\":37,\"end\":39}},{\"generatedToken\":{\"token\":\"\u2581de\",\"logprob\":0.0,\"raw_logprob\":-8.886678842827678E-4},\"topTokens\":null,\"textRange\":{\"start\":39,\"end\":42}},{\"generatedToken\":{\"token\":\"\u2581const\",\"logprob\":0.0,\"raw_logprob\":-0.031633295118808746},\"topTokens\":null,\"textRange\":{\"start\":42,\"end\":48}},{\"generatedToken\":{\"token\":\"ru\",\"logprob\":0.0,\"raw_logprob\":-1.5497195136049413E-6},\"topTokens\":null,\"textRange\":{\"start\":48,\"end\":50}},{\"generatedToken\":{\"token\":\"ir\",\"logprob\":0.0,\"raw_logprob\":-8.106198947643861E-6},\"topTokens\":null,\"textRange\":{\"start\":50,\"end\":52}},{\"generatedToken\":{\"token\":\"\u2581\",\"logprob\":0.0,\"raw_logprob\":-3.969590397900902E-5},\"topTokens\":null,\"textRange\":{\"start\":52,\"end\":53}},{\"generatedToken\":{\"token\":\"y\",\"logprob\":0.0,\"raw_logprob\":-0.006035791710019112},\"topTokens\":null,\"textRange\":{\"start\":53,\"end\":54}},{\"generatedToken\":{\"token\":\"\u2581\",\"logprob\":0.0,\"raw_logprob\":-0.14323875308036804},\"topTokens\":null,\"textRange\":{\"start\":54,\"end\":55}},{\"generatedToken\":{\"token\":\"esca\",\"logprob\":0.0,\"raw_logprob\":-0.87629234790802},\"topTokens\":null,\"textRange\":{\"start\":55,\"end\":59}},{\"generatedToken\":{\"token\":\"lado\",\"logprob\":0.0,\"raw_logprob\":-0.2959917485713959},\"topTokens\":null,\"textRange\":{\"start\":59,\"end\":63}},{\"generatedToken\":{\"token\":\"\u2581de\",\"logprob\":0.0,\"raw_logprob\":-0.07214847207069397},\"topTokens\":null,\"textRange\":{\"start\":63,\"end\":66}},{\"generatedToken\":{\"token\":\"\u2581a\",\"logprob\":0.0,\"raw_logprob\":-0.00686619384214282},\"topTokens\":null,\"textRange\":{\"start\":66,\"end\":68}},{\"generatedToken\":{\"token\":\"plica\",\"logprob\":0.0,\"raw_logprob\":-6.571040721610188E-4},\"topTokens\":null,\"textRange\":{\"start\":68,\"end\":73}},{\"generatedToken\":{\"token\":\"ciones\",\"logprob\":0.0,\"raw_logprob\":-1.9131260341964662E-4},\"topTokens\":null,\"textRange\":{\"start\":73,\"end\":79}},{\"generatedToken\":{\"token\":\"\u2581\",\"logprob\":0.0,\"raw_logprob\":-0.6169446110725403},\"topTokens\":null,\"textRange\":{\"start\":79,\"end\":80}},{\"generatedToken\":{\"token\":\"generative\",\"logprob\":0.0,\"raw_logprob\":-0.32334673404693604},\"topTokens\":null,\"textRange\":{\"start\":80,\"end\":90}},{\"generatedToken\":{\"token\":\"s\u2581de\",\"logprob\":0.0,\"raw_logprob\":-0.6529382467269897},\"topTokens\":null,\"textRange\":{\"start\":90,\"end\":94}},{\"generatedToken\":{\"token\":\"\u2581AI\",\"logprob\":0.0,\"raw_logprob\":-0.8433590531349182},\"topTokens\":null,\"textRange\":{\"start\":94,\"end\":97}},{\"generatedToken\":{\"token\":\"\u2581con\",\"logprob\":0.0,\"raw_logprob\":-0.010683131404221058},\"topTokens\":null,\"textRange\":{\"start\":97,\"end\":101}},{\"generatedToken\":{\"token\":\"\u2581model\",\"logprob\":0.0,\"raw_logprob\":-0.07065271586179733},\"topTokens\":null,\"textRange\":{\"start\":101,\"end\":107}},{\"generatedToken\":{\"token\":\"os\",\"logprob\":0.0,\"raw_logprob\":-0.003825371852144599},\"topTokens\":null,\"textRange\":{\"start\":107,\"end\":109}},{\"generatedToken\":{\"token\":\"\u2581base\",\"logprob\":0.0,\"raw_logprob\":-0.20432710647583008},\"topTokens\":null,\"textRange\":{\"start\":109,\"end\":114}},{\"generatedToken\":{\"token\":\"\u2581\",\"logprob\":0.0,\"raw_logprob\":-0.024862026795744896},\"topTokens\":null,\"textRange\":{\"start\":114,\"end\":115}},{\"generatedToken\":{\"token\":\"(\",\"logprob\":0.0,\"raw_logprob\":-5.646541831083596E-4},\"topTokens\":null,\"textRange\":{\"start\":115,\"end\":116}},{\"generatedToken\":{\"token\":\"FM\",\"logprob\":0.0,\"raw_logprob\":-3.064401389565319E-4},\"topTokens\":null,\"textRange\":{\"start\":116,\"end\":118}},{\"generatedToken\":{\"token\":\"s\",\"logprob\":0.0,\"raw_logprob\":-0.022001003846526146},\"topTokens\":null,\"textRange\":{\"start\":118,\"end\":119}},{\"generatedToken\":{\"token\":\")\",\"logprob\":0.0,\"raw_logprob\":-0.001255200942978263},\"topTokens\":null,\"textRange\":{\"start\":119,\"end\":120}},{\"generatedToken\":{\"token\":\"'.\",\"logprob\":0.0,\"raw_logprob\":-0.1687663495540619},\"topTokens\":null,\"textRange\":{\"start\":120,\"end\":122}},{\"generatedToken\":{\"token\":\"<|endoftext|>\",\"logprob\":0.0,\"raw_logprob\":-0.0010474200826138258},\"topTokens\":null,\"textRange\":{\"start\":122,\"end\":122}}]},\"finishReason\":{\"reason\":\"endoftext\"}}]}"
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '10117'
      Content-Type:
      - application/json
      Date:
      - Tue, 11 Jun 2024 00:34:37 GMT
      X-Amzn-Bedrock-Input-Token-Count:
      - '26'
      X-Amzn-Bedrock-Invocation-Latency:
      - '696'
      X-Amzn-Bedrock-Output-Token-Count:
      - '42'
      x-amzn-RequestId:
      - 998dab0e-94b2-49e4-aa03-ee2ee25437f0
    status:
      code: 200
      message: OK
version: 1
