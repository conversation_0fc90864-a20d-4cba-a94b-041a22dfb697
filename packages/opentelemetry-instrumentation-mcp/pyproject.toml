[tool.coverage.run]
branch = true
source = ["opentelemetry/instrumentation/mcp"]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.poetry]
name = "opentelemetry-instrumentation-mcp"
version = "0.43.1"
description = "OpenTelemetry mcp instrumentation"
authors = ["<PERSON> <<EMAIL>>"]
repository = "https://github.com/traceloop/openllmetry/tree/main/packages/opentelemetry-instrumentation-mcp"
license = "Apache-2.0"
readme = "README.md"

[[tool.poetry.packages]]
include = "opentelemetry/instrumentation/mcp"

[tool.poetry.dependencies]
python = ">=3.10,<4"
opentelemetry-api = "^1.28.0"
opentelemetry-instrumentation = ">=0.50b0"
opentelemetry-semantic-conventions = ">=0.50b0"
opentelemetry-semantic-conventions-ai = "0.4.11"
opentelemetry-exporter-otlp = "1.34.1"

[tool.poetry.group.dev.dependencies]
autopep8 = "^2.2.0"
flake8 = "7.1.1"
pytest = "^8.2.2"
pytest-sugar = "1.0.0"

[tool.poetry.group.test.dependencies]
mcp = { version = "^1.3.0", python = ">=3.10,<=3.13" }
pytest = "^8.2.2"
pytest-sugar = "1.0.0"
pytest-recording = "^0.13.1"
opentelemetry-sdk = "^1.27.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.extras]
instruments = ["mcp"]

[tool.poetry.plugins."opentelemetry_instrumentor"]
mcp = "opentelemetry.instrumentation.mcp:McpInstrumentor"

[tool.pytest.ini_options]
asyncio_mode = "auto"
pythonpath = [
  "src",
  ".",
]
testpaths = [
  "tests",
]
