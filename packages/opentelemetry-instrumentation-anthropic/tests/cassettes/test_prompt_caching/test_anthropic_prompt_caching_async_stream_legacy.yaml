interactions:
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": [{"type":
      "text", "text": "test_anthropic_prompt_caching_async_stream <- IGNORE THIS.
      ARTICLES START ON THE NEXT LINE\nOpen-Source Library OpenLLMetry Brings LLM
      Functionality to OpenTelemetry\nTraceloop, a YCombinator-backed company, has
      announced the release of OpenLLMetry, a new open-source\nlibrary designed to
      extend OpenTelemetry with Large Language Model (LLM) functionality. This innovative\ntool
      aims to bridge the gap between traditional application monitoring and the rapidly
      evolving field\nof AI-powered systems. OpenLLMetry builds upon the widely-adopted
      OpenTelemetry framework, which provides\na standardized approach to collecting
      and exporting telemetry data from cloud-native applications. By\nintegrating
      LLM-specific features, OpenLLMetry enables developers to gain deeper insights
      into\nthe performance and behavior of AI models within their applications.\n\nKey
      features of OpenLLMetry include:\n\nLLM-specific metrics and traces\nSeamless
      integration with existing OpenTelemetry setups\nSupport for popular LLM frameworks
      and platforms\n\nThe open-source nature of OpenLLMetry is expected to foster
      community contributions and rapid adoption\namong developers working with LLMs.
      As AI continues to transform the software landscape, tools\nlike OpenLLMetry
      are poised to play a vital role in ensuring the reliability and performance
      of\nnext-generation applications.\n\n========================================================================\n\nMajor
      LLM Providers Introduce Prompt Caching, Boosting Speed and Reducing Costs\n\nIn
      a significant development for the artificial intelligence industry, leading
      Large Language Model (LLM) providers,\nincluding Anthropic, have announced the
      implementation of prompt caching. This new feature promises to dramatically\nimprove
      the speed and cost-effectiveness of LLM API calls, particularly for applications
      with repetitive prompt patterns.\n\nUnderstanding Prompt Caching\nPrompt caching
      is a technique that allows LLM providers to store and quickly retrieve responses
      for frequently used prompts.\nInstead of processing the same or similar prompts
      repeatedly, the system can serve pre-computed responses,\nsignificantly reducing
      computation time and resources.\n\nBenefits of Prompt Caching\n1. Improved Response
      Times\n\nWith prompt caching, response times for cached prompts can be reduced
      from seconds to milliseconds. This dramatic speed\nimprovement enables near-instantaneous
      responses in many scenarios, enhancing user experience in AI-powered applications.\n\n2.
      Cost Reduction\nBy eliminating the need to reprocess identical or highly similar
      prompts, prompt caching can substantially reduce the\ncomputational resources
      required. This efficiency translates directly into cost savings for developers
      and businesses\nutilizing LLM APIs.\n\n3. Scalability\nThe reduced computational
      load allows LLM providers to handle a higher volume of requests with existing
      infrastructure,\nimproving the scalability of their services.\n\nUse Cases and
      Impact\nPrompt caching is particularly beneficial for applications with repetitive
      prompt patterns. Some key use cases include:\n\nCustomer service chatbots handling
      common queries\nContent moderation systems processing similar types of content\nLanguage
      translation services for frequently translated phrases or sentences\nAutomated
      coding assistants dealing with standard programming tasks\n\nImplementation
      by Major Providers\nWhile the specific implementation details vary among providers,
      the general approach involves:\n\nIdentifying frequently used prompts\nStoring
      pre-computed responses\nImplementing efficient lookup mechanisms\nBalancing
      cache freshness with performance gains\n\nAnthropic, known for its Claude AI
      model, has been at the forefront of this technology.\n\nFuture Implications\nThe
      introduction of prompt caching by major LLM providers is likely to have far-reaching
      effects on the AI industry:\n\nBroader Adoption: Reduced costs and improved
      performance could lead to wider adoption of LLM technologies across various
      sectors.\nNew Application Paradigms: Developers may create new types of applications
      that leverage the near-instantaneous response times of cached prompts.\nEvolution
      of Pricing Models: LLM providers might introduce new pricing structures that
      reflect the efficiency gains of prompt caching.\n\nAs the technology matures,
      we can expect to see further refinements and innovative applications of prompt
      caching, potentially\nreshaping the landscape of AI-powered services and applications.\n\n========================================================================\n\n\ud83d\udcca
      Why Unit Testing is Non-Negotiable in Software Development \ud83d\udda5\ufe0f\nAs
      a software professional, I can''t stress enough how crucial unit testing is
      to our craft. Here''s why it''s a must-have practice:\n\n\ud83d\udc1b Catches
      bugs early: Identify and fix issues before they snowball into major problems.\n\ud83d\udcb0
      Saves time and money: Less debugging time means faster development and lower
      costs.\n\ud83c\udfd7\ufe0f Improves code quality: Writing testable code inherently
      leads to better architecture.\n\ud83d\udd04 Facilitates refactoring: Tests give
      you confidence to improve your code without breaking functionality.\n\ud83d\udcda
      Serves as documentation: Well-written tests explain how your code should behave.\n\ud83e\udd1d
      Enhances collaboration: New team members can understand and contribute to the
      codebase faster.\n\nRemember: The time invested in unit testing pays off multifold
      in the long run. It''s not just good practice\u2014it''s professional responsibility.\nWhat''s
      your take on unit testing? Share your experiences below! \ud83d\udc47", "cache_control":
      {"type": "ephemeral"}}]}], "model": "claude-3-5-sonnet-20240620", "stream":
      true, "system": [{"type": "text", "text": "You help generate concise summaries
      of news articles and blog posts that user sends you."}]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      anthropic-version:
      - '2023-06-01'
      connection:
      - keep-alive
      content-length:
      - '5999'
      content-type:
      - application/json
      host:
      - api.anthropic.com
      user-agent:
      - AsyncAnthropic/Python 0.49.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 0.49.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.9.6
      x-stainless-timeout:
      - NOT_GIVEN
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: 'event: message_start

        data: {"type":"message_start","message":{"id":"msg_01KQCu5jXyou55u6YFNk6uqu","type":"message","role":"assistant","model":"claude-3-5-sonnet-20240620","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":4,"cache_creation_input_tokens":1167,"cache_read_input_tokens":0,"output_tokens":1}}     }


        event: content_block_start

        data: {"type":"content_block_start","index":0,"content_block":{"type":"text","text":""}               }


        event: ping

        data: {"type": "ping"}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"Here"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        are concise summ"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"aries
        of the three articles:\n\n1."}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        OpenLLMetry"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":":
        New Open-Source Library"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        for LLM Monitoring"}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"\n\nTrace"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"loop
        released OpenLLMetry"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":",
        an open-source"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        library extending OpenTelemetry"}            }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        with LLM functionality"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":".
        Key features:\n- L"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"LM-specific
        metrics"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        and traces\n- Integration"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        with existing OpenTel"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"emetry
        setups"}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"\n-
        Support for popular L"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"LM
        frameworks"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"\nAims
        to provide deeper insights into"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        AI model performance in"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        applications.\n\n2."}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Major LLM"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Providers Introduce Prompt"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Caching\n\nLeading"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        LLM providers,"}            }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        including Anthropic,"}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        have implemented prompt caching"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        to improve speed and reduce costs."}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Benefits:\n-"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Faster response times (milliseconds vs seconds"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":")\n-
        Lower computational costs\n-"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Improved scalability"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"\nParticularly
        useful for applications with repetitive"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        prompts like chatbots an"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        content moderation. Expected to drive wider L"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"LM
        adoption and new"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        application paradigms."}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"\n\n3.
        Importance of Unit Testing in"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Software Development\n\nA software professional advocates"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        for unit testing as crucial"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        to development:\n- Catches bugs"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        early\n- Saves"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        time and money\n-"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Improves code quality an"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        architecture\n- Facilit"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"ates
        refactoring"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"\n-
        Acts as documentation\n-"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Enhances team collaboration\nEmphasize"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        as a professional responsibility with"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        long-term benefits."}      }


        event: content_block_stop

        data: {"type":"content_block_stop","index":0            }


        event: message_delta

        data: {"type":"message_delta","delta":{"stop_reason":"end_turn","stop_sequence":null},"usage":{"output_tokens":289}            }


        event: message_stop

        data: {"type":"message_stop"     }


        '
    headers:
      CF-RAY:
      - 920b09edcfa8ecf3-ALA
      Cache-Control:
      - no-cache
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Sat, 15 Mar 2025 09:38:37 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-organization-id:
      - 04aa8588-6567-40cb-9042-a54b20ebaf4f
      anthropic-ratelimit-input-tokens-limit:
      - '400000'
      anthropic-ratelimit-input-tokens-remaining:
      - '399000'
      anthropic-ratelimit-input-tokens-reset:
      - '2025-03-15T09:38:36Z'
      anthropic-ratelimit-output-tokens-limit:
      - '80000'
      anthropic-ratelimit-output-tokens-remaining:
      - '79000'
      anthropic-ratelimit-output-tokens-reset:
      - '2025-03-15T09:38:37Z'
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2025-03-15T09:38:36Z'
      anthropic-ratelimit-tokens-limit:
      - '480000'
      anthropic-ratelimit-tokens-remaining:
      - '478000'
      anthropic-ratelimit-tokens-reset:
      - '2025-03-15T09:38:36Z'
      cf-cache-status:
      - DYNAMIC
      request-id:
      - req_01NvQN8EU2zb9EcsSvxtFYTt
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": [{"type":
      "text", "text": "test_anthropic_prompt_caching_async_stream <- IGNORE THIS.
      ARTICLES START ON THE NEXT LINE\nOpen-Source Library OpenLLMetry Brings LLM
      Functionality to OpenTelemetry\nTraceloop, a YCombinator-backed company, has
      announced the release of OpenLLMetry, a new open-source\nlibrary designed to
      extend OpenTelemetry with Large Language Model (LLM) functionality. This innovative\ntool
      aims to bridge the gap between traditional application monitoring and the rapidly
      evolving field\nof AI-powered systems. OpenLLMetry builds upon the widely-adopted
      OpenTelemetry framework, which provides\na standardized approach to collecting
      and exporting telemetry data from cloud-native applications. By\nintegrating
      LLM-specific features, OpenLLMetry enables developers to gain deeper insights
      into\nthe performance and behavior of AI models within their applications.\n\nKey
      features of OpenLLMetry include:\n\nLLM-specific metrics and traces\nSeamless
      integration with existing OpenTelemetry setups\nSupport for popular LLM frameworks
      and platforms\n\nThe open-source nature of OpenLLMetry is expected to foster
      community contributions and rapid adoption\namong developers working with LLMs.
      As AI continues to transform the software landscape, tools\nlike OpenLLMetry
      are poised to play a vital role in ensuring the reliability and performance
      of\nnext-generation applications.\n\n========================================================================\n\nMajor
      LLM Providers Introduce Prompt Caching, Boosting Speed and Reducing Costs\n\nIn
      a significant development for the artificial intelligence industry, leading
      Large Language Model (LLM) providers,\nincluding Anthropic, have announced the
      implementation of prompt caching. This new feature promises to dramatically\nimprove
      the speed and cost-effectiveness of LLM API calls, particularly for applications
      with repetitive prompt patterns.\n\nUnderstanding Prompt Caching\nPrompt caching
      is a technique that allows LLM providers to store and quickly retrieve responses
      for frequently used prompts.\nInstead of processing the same or similar prompts
      repeatedly, the system can serve pre-computed responses,\nsignificantly reducing
      computation time and resources.\n\nBenefits of Prompt Caching\n1. Improved Response
      Times\n\nWith prompt caching, response times for cached prompts can be reduced
      from seconds to milliseconds. This dramatic speed\nimprovement enables near-instantaneous
      responses in many scenarios, enhancing user experience in AI-powered applications.\n\n2.
      Cost Reduction\nBy eliminating the need to reprocess identical or highly similar
      prompts, prompt caching can substantially reduce the\ncomputational resources
      required. This efficiency translates directly into cost savings for developers
      and businesses\nutilizing LLM APIs.\n\n3. Scalability\nThe reduced computational
      load allows LLM providers to handle a higher volume of requests with existing
      infrastructure,\nimproving the scalability of their services.\n\nUse Cases and
      Impact\nPrompt caching is particularly beneficial for applications with repetitive
      prompt patterns. Some key use cases include:\n\nCustomer service chatbots handling
      common queries\nContent moderation systems processing similar types of content\nLanguage
      translation services for frequently translated phrases or sentences\nAutomated
      coding assistants dealing with standard programming tasks\n\nImplementation
      by Major Providers\nWhile the specific implementation details vary among providers,
      the general approach involves:\n\nIdentifying frequently used prompts\nStoring
      pre-computed responses\nImplementing efficient lookup mechanisms\nBalancing
      cache freshness with performance gains\n\nAnthropic, known for its Claude AI
      model, has been at the forefront of this technology.\n\nFuture Implications\nThe
      introduction of prompt caching by major LLM providers is likely to have far-reaching
      effects on the AI industry:\n\nBroader Adoption: Reduced costs and improved
      performance could lead to wider adoption of LLM technologies across various
      sectors.\nNew Application Paradigms: Developers may create new types of applications
      that leverage the near-instantaneous response times of cached prompts.\nEvolution
      of Pricing Models: LLM providers might introduce new pricing structures that
      reflect the efficiency gains of prompt caching.\n\nAs the technology matures,
      we can expect to see further refinements and innovative applications of prompt
      caching, potentially\nreshaping the landscape of AI-powered services and applications.\n\n========================================================================\n\n\ud83d\udcca
      Why Unit Testing is Non-Negotiable in Software Development \ud83d\udda5\ufe0f\nAs
      a software professional, I can''t stress enough how crucial unit testing is
      to our craft. Here''s why it''s a must-have practice:\n\n\ud83d\udc1b Catches
      bugs early: Identify and fix issues before they snowball into major problems.\n\ud83d\udcb0
      Saves time and money: Less debugging time means faster development and lower
      costs.\n\ud83c\udfd7\ufe0f Improves code quality: Writing testable code inherently
      leads to better architecture.\n\ud83d\udd04 Facilitates refactoring: Tests give
      you confidence to improve your code without breaking functionality.\n\ud83d\udcda
      Serves as documentation: Well-written tests explain how your code should behave.\n\ud83e\udd1d
      Enhances collaboration: New team members can understand and contribute to the
      codebase faster.\n\nRemember: The time invested in unit testing pays off multifold
      in the long run. It''s not just good practice\u2014it''s professional responsibility.\nWhat''s
      your take on unit testing? Share your experiences below! \ud83d\udc47", "cache_control":
      {"type": "ephemeral"}}]}], "model": "claude-3-5-sonnet-20240620", "stream":
      true, "system": [{"type": "text", "text": "You help generate concise summaries
      of news articles and blog posts that user sends you."}]}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      anthropic-version:
      - '2023-06-01'
      connection:
      - keep-alive
      content-length:
      - '5999'
      content-type:
      - application/json
      host:
      - api.anthropic.com
      user-agent:
      - AsyncAnthropic/Python 0.49.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 0.49.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.9.6
      x-stainless-timeout:
      - NOT_GIVEN
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: 'event: message_start

        data: {"type":"message_start","message":{"id":"msg_01GZo7EAMfEuzRqTKrFANNpA","type":"message","role":"assistant","model":"claude-3-5-sonnet-20240620","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":4,"cache_creation_input_tokens":0,"cache_read_input_tokens":1167,"output_tokens":1}}              }


        event: content_block_start

        data: {"type":"content_block_start","index":0,"content_block":{"type":"text","text":""}          }


        event: ping

        data: {"type": "ping"}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"Here"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        are concise summ"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"aries
        of the three"}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        articles:\n\n1."}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        OpenLLMetry"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":":
        New Open-Source Library"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        for LLM Monitoring"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"\n\nTraceloop"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        released OpenLLMetry"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":",
        an open-source"}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        library extending OpenTelemetry"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        with LLM functionality"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":".
        Key features include LLM-specific metrics"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        and traces, integration with existing set"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"ups,
        and support for popular LLM frameworks"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":".
        It aims to provide deeper"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        insights into AI model performance within"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        applications.\n\n2. Major LLM"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Providers Introduce Prompt Caching\n\nLeading"}            }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        LLM providers, including Anthropic,"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        have implemented prompt caching to improve speed and reduce"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        costs of API calls. This technique"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        stores responses for frequent prompts, dramatically"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        reducing response times and computational resources."}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Benefits include improved scalability and cost-"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"effectiveness,
        particularly for applications with repetitive"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        prompt patterns.\n\n3."}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Importance of Unit Testing in Software Development\n\nThe"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        article emphasizes the critical role of unit testing in"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        software development. Key benefits include early"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        bug detection, time an"}            }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        cost savings, improved code quality, easier"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        refactoring, code documentation, and enhanced team"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        collaboration. The author argues that unit testing is a"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        professional responsibility that pays off significantly"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        in the long run."}          }


        event: content_block_stop

        data: {"type":"content_block_stop","index":0}


        event: message_delta

        data: {"type":"message_delta","delta":{"stop_reason":"end_turn","stop_sequence":null},"usage":{"output_tokens":256}           }


        event: message_stop

        data: {"type":"message_stop"            }


        '
    headers:
      CF-RAY:
      - 920b0a119eb4ecf3-ALA
      Cache-Control:
      - no-cache
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Sat, 15 Mar 2025 09:38:42 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-organization-id:
      - 04aa8588-6567-40cb-9042-a54b20ebaf4f
      anthropic-ratelimit-input-tokens-limit:
      - '400000'
      anthropic-ratelimit-input-tokens-remaining:
      - '399000'
      anthropic-ratelimit-input-tokens-reset:
      - '2025-03-15T09:38:41Z'
      anthropic-ratelimit-output-tokens-limit:
      - '80000'
      anthropic-ratelimit-output-tokens-remaining:
      - '79000'
      anthropic-ratelimit-output-tokens-reset:
      - '2025-03-15T09:38:42Z'
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2025-03-15T09:38:41Z'
      anthropic-ratelimit-tokens-limit:
      - '480000'
      anthropic-ratelimit-tokens-remaining:
      - '478000'
      anthropic-ratelimit-tokens-reset:
      - '2025-03-15T09:38:41Z'
      cf-cache-status:
      - DYNAMIC
      request-id:
      - req_01RAACcnSfUq4RTDRa3yWxdL
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
version: 1
