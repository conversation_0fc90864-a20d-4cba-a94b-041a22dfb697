interactions:
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "Tell me
      a joke about OpenTelemetry"}], "model": "claude-3-haiku-20240307", "stream":
      true}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      anthropic-version:
      - '2023-06-01'
      connection:
      - keep-alive
      content-length:
      - '153'
      content-type:
      - application/json
      host:
      - api.anthropic.com
      user-agent:
      - Anthropic/Python 0.36.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 0.36.2
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.3
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: 'event: message_start

        data: {"type":"message_start","message":{"id":"msg_01MXWxhWoPSgrYhjTuMDM6F1","type":"message","role":"assistant","model":"claude-3-haiku-20240307","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":17,"output_tokens":3}}         }


        event: content_block_start

        data: {"type":"content_block_start","index":0,"content_block":{"type":"text","text":""}}


        event: ping

        data: {"type": "ping"}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"Here''s
        an"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        OpenTelemet"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"ry-"}            }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"themed
        joke for"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        you:\n\nWhy"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        did the developer"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        feel"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        so"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        lost"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        when"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        using"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        OpenTelemet"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"ry?"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"\nThey"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        were"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        in"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        a"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Span"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        of Confusion"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"!\n\nThe"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        idea"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        behind this"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        joke"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        is that OpenT"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"elemetry
        uses"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        the"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        concept"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        of \""}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"spans\"
        to represent"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        individual"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        operations or requests within"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        a distribute"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        system. When"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        a developer is"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        first"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        getting starte"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        with OpenTel"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"emetry,
        they"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        may feel a"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        bit dis"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"oriented
        or"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        lost trying"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        to understand all"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        the different spans"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        and how"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        they fit"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        together -"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        hence"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        the \"Span"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        of Confusion\""}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        p"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"un."}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"\n\nHopefully
        this gives"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        you a chuck"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"le
        an"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        provides"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        a ligh"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"thearted
        introduction"}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        to some"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        of the"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        key concepts in"}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        OpenTelemet"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"ry."}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        Let me know if"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        you''"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        like to"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        hear any other tech"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"-"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"theme"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        jokes!"}          }


        event: content_block_stop

        data: {"type":"content_block_stop","index":0 }


        event: message_delta

        data: {"type":"message_delta","delta":{"stop_reason":"end_turn","stop_sequence":null},"usage":{"output_tokens":171}           }


        event: message_stop

        data: {"type":"message_stop"          }


        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8d5c8d70d8c09464-SJC
      Cache-Control:
      - no-cache
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Sun, 20 Oct 2024 22:47:56 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '50'
      anthropic-ratelimit-requests-remaining:
      - '49'
      anthropic-ratelimit-requests-reset:
      - '2024-10-20T22:48:55Z'
      anthropic-ratelimit-tokens-limit:
      - '50000'
      anthropic-ratelimit-tokens-remaining:
      - '49000'
      anthropic-ratelimit-tokens-reset:
      - '2024-10-20T22:47:57Z'
      request-id:
      - req_01JUK6ATKHDAaXF2Bs7Xprjv
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
version: 1
