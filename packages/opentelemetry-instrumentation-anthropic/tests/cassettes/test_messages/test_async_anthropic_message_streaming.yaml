interactions:
- request:
    body: '{"max_tokens": 1024, "messages": [{"role": "user", "content": "Tell me
      a joke about OpenTelemetry"}], "model": "claude-3-haiku-20240307", "stream":
      true}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      anthropic-version:
      - '2023-06-01'
      connection:
      - keep-alive
      content-length:
      - '153'
      content-type:
      - application/json
      host:
      - api.anthropic.com
      user-agent:
      - AsyncAnthropic/Python 0.36.2
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 0.36.2
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.3
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: 'event: message_start

        data: {"type":"message_start","message":{"id":"msg_016o6A7zDmgjucf5mWv1rrPD","type":"message","role":"assistant","model":"claude-3-haiku-20240307","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":17,"output_tokens":3}}             }


        event: content_block_start

        data: {"type":"content_block_start","index":0,"content_block":{"type":"text","text":""}}


        event: ping

        data: {"type": "ping"}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"Here''s
        an"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        OpenTelemet"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"ry-"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"themed
        joke for"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        you:\n\nWhy"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        was"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        the OpenTel"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"emetry
        tra"}            }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"cer
        so"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        tire"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d?
        Because it ha"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        been"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        tr"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"acing
        all"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        day"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"!\n\nIn"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        the"}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        worl"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        of distribute"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        tr"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"acing
        an"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        observ"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"ability,
        OpenT"}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"elemetry
        is"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        the tracing library"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        that"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        just"}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        keeps going"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        an"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"d
        going,"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        collecting"}            }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        data from"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        all your"}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        microservices."}           }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        But"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        even"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        the"}    }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        most di"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"ligent
        tra"}        }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"cer
        needs"}               }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"text_delta","text":"
        a break sometimes!"}  }


        event: content_block_stop

        data: {"type":"content_block_stop","index":0}


        event: message_delta

        data: {"type":"message_delta","delta":{"stop_reason":"end_turn","stop_sequence":null},"usage":{"output_tokens":92}        }


        event: message_stop

        data: {"type":"message_stop" }


        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8d5c8d7bce36cec9-SJC
      Cache-Control:
      - no-cache
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Sun, 20 Oct 2024 22:47:58 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-ratelimit-requests-limit:
      - '50'
      anthropic-ratelimit-requests-remaining:
      - '49'
      anthropic-ratelimit-requests-reset:
      - '2024-10-20T22:48:55Z'
      anthropic-ratelimit-tokens-limit:
      - '50000'
      anthropic-ratelimit-tokens-remaining:
      - '49000'
      anthropic-ratelimit-tokens-reset:
      - '2024-10-20T22:47:59Z'
      request-id:
      - req_01F1o1MC5ME8uRqRyfy8NVC3
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
version: 1
