interactions:
- request:
    body: '{"max_tokens": 2048, "messages": [{"role": "user", "content": [{"type":
      "text", "text": "How many times does the letter ''r'' appear in the word strawberry?"}]}],
      "model": "claude-3-7-sonnet-20250219", "stream": true, "thinking": {"type":
      "enabled", "budget_tokens": 1024}}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      anthropic-version:
      - '2023-06-01'
      connection:
      - keep-alive
      content-length:
      - '271'
      content-type:
      - application/json
      host:
      - api.anthropic.com
      user-agent:
      - AsyncAnthropic/Python 0.49.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 0.49.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.9.6
      x-stainless-timeout:
      - NOT_GIVEN
    method: POST
    uri: https://api.anthropic.com/v1/messages
  response:
    body:
      string: 'event: message_start

        data: {"type":"message_start","message":{"id":"msg_017MhNmn5nUm4C53tXC7Yh2y","type":"message","role":"assistant","model":"claude-3-7-sonnet-20250219","content":[],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":52,"cache_creation_input_tokens":0,"cache_read_input_tokens":0,"output_tokens":5}}       }


        event: content_block_start

        data: {"type":"content_block_start","index":0,"content_block":{"type":"thinking","thinking":"","signature":""}            }


        event: ping

        data: {"type": "ping"}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"I
        need to count"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"
        the occurrences of the letter ''r'' in"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"
        the word \"strawberry\".\n\nThe word is:"}   }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"
        s-t-r-a-w-b-"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"e-r-r-y\n\nGoing
        through each letter:"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"\n-
        s: not an ''r''\n- t: not an"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"
        ''r''\n- r: this is an ''r''"}}


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"
        (first occurrence)\n- a: not an ''"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"r''\n-
        w: not an ''"}          }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"r''\n-
        b: not an ''"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"r''\n-
        e: not an ''r''\n- r"}         }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":":
        this is an ''r'' (second occurrence)\n- r"}       }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":":
        this is an ''r'' (thir"}  }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"d
        occurrence)\n- y: not an ''r''\n\nSo the"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"
        letter ''r'' appears 3 times in the word \"straw"} }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"thinking_delta","thinking":"berry\"."}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":0,"delta":{"type":"signature_delta","signature":"ErUBCkYIARgCIkDGs9QuvKv+/vunX8M+9GOMaupWZ4e+0VUfSAEyLTYlfQL0KPGj8wxHJbrjLYQsF8KPMsMgzuPuv+P2t/fVHFsREgzZdrZSBMTTENI+/XEaDOpE756oOYdY5YpUmCIwP+gW0SpfpW6RPFTLdiH4OgsVtyClBUGP/wlIanxo6HJc+BcSzVhJ5b6T/GPenNKlKh2ObiIO8fjNb+SND12E2/bXiTDd2coDk5EjQx2PHA=="}          }


        event: content_block_stop

        data: {"type":"content_block_stop","index":0           }


        event: content_block_start

        data: {"type":"content_block_start","index":1,"content_block":{"type":"text","text":""}      }


        event: content_block_delta

        data: {"type":"content_block_delta","index":1,"delta":{"type":"text_delta","text":"The
        letter ''r'' appears 3 times in the"}              }


        event: content_block_delta

        data: {"type":"content_block_delta","index":1,"delta":{"type":"text_delta","text":"
        word \"strawberry\".\n\nYou can see them in the"}     }


        event: content_block_delta

        data: {"type":"content_block_delta","index":1,"delta":{"type":"text_delta","text":"
        spelling: st(r)awbe(r)("}             }


        event: content_block_delta

        data: {"type":"content_block_delta","index":1,"delta":{"type":"text_delta","text":"r)y"}         }


        event: content_block_stop

        data: {"type":"content_block_stop","index":1   }


        event: message_delta

        data: {"type":"message_delta","delta":{"stop_reason":"end_turn","stop_sequence":null},"usage":{"output_tokens":238}      }


        event: message_stop

        data: {"type":"message_stop"      }


        '
    headers:
      CF-RAY:
      - 920c3bd8fc4bece3-ALA
      Cache-Control:
      - no-cache
      Connection:
      - keep-alive
      Content-Type:
      - text/event-stream; charset=utf-8
      Date:
      - Sat, 15 Mar 2025 13:07:26 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Robots-Tag:
      - none
      anthropic-organization-id:
      - 04aa8588-6567-40cb-9042-a54b20ebaf4f
      anthropic-ratelimit-input-tokens-limit:
      - '200000'
      anthropic-ratelimit-input-tokens-remaining:
      - '141000'
      anthropic-ratelimit-input-tokens-reset:
      - '2025-03-15T13:07:44Z'
      anthropic-ratelimit-output-tokens-limit:
      - '80000'
      anthropic-ratelimit-output-tokens-remaining:
      - '66000'
      anthropic-ratelimit-output-tokens-reset:
      - '2025-03-15T13:07:36Z'
      anthropic-ratelimit-requests-limit:
      - '4000'
      anthropic-ratelimit-requests-remaining:
      - '3999'
      anthropic-ratelimit-requests-reset:
      - '2025-03-15T13:07:26Z'
      anthropic-ratelimit-tokens-limit:
      - '280000'
      anthropic-ratelimit-tokens-remaining:
      - '207000'
      anthropic-ratelimit-tokens-reset:
      - '2025-03-15T13:07:36Z'
      cf-cache-status:
      - DYNAMIC
      request-id:
      - req_01DEtrHfMMfnTfmUABPH5dyK
      via:
      - 1.1 google
    status:
      code: 200
      message: OK
version: 1
