from opentelemetry.sdk.trace import SpanProcessor, ReadableSpan
from opentelemetry import metrics
from opentelemetry.metrics import Counter


class GenAITraceCountProcessor(SpanProcessor):
    def __init__(self):
        self._meter = metrics.get_meter("traceloop.genai.metrics")
        self._genai_trace_counter: Counter = self._meter.create_counter(
            name="genai_trace_count",
            description="Number of completed GenAI traces",
            unit="1"
        )

    def on_start(self, span: ReadableSpan, parent_context=None) -> None:
        pass

    def on_end(self, span: ReadableSpan) -> None:
        if span.parent is None:
            self._genai_trace_counter.add(1)

    def shutdown(self) -> None:
        pass

    def force_flush(self, timeout_millis: int = 30000) -> bool:
        return True 