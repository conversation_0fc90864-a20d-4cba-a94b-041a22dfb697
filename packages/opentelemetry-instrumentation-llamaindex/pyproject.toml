[tool.coverage.run]
branch = true
source = ["opentelemetry/instrumentation/llamaindex"]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.poetry]
name = "opentelemetry-instrumentation-llamaindex"
version = "0.43.1"
description = "OpenTelemetry LlamaIndex instrumentation"
authors = [
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
]
repository = "https://github.com/traceloop/openllmetry/tree/main/packages/opentelemetry-instrumentation-llamaindex"
license = "Apache-2.0"
readme = "README.md"

[[tool.poetry.packages]]
include = "opentelemetry/instrumentation/llamaindex"

[tool.poetry.dependencies]
python = ">=3.9,<4"
opentelemetry-api = "^1.28.0"
opentelemetry-instrumentation = ">=0.50b0"
opentelemetry-semantic-conventions = ">=0.50b0"
opentelemetry-semantic-conventions-ai = "0.4.11"
inflection = "^0.5.1"

[tool.poetry.group.dev.dependencies]
autopep8 = "^2.2.0"
flake8 = "7.0.0"

[tool.poetry.group.test.dependencies]
vcrpy = "^6.0.1"
pytest-recording = "^0.13.1"
pytest-asyncio = "^0.23.7"
chromadb = "^0.5.23"
openai = "^1.52.2"
opentelemetry-sdk = "^1.27.0"
llama-index = "^0.12.6"
llama-index-postprocessor-cohere-rerank = "^0.3.0"
opentelemetry-instrumentation-openai = { path = "../opentelemetry-instrumentation-openai", develop = true }
opentelemetry-instrumentation-cohere = { path = "../opentelemetry-instrumentation-cohere", develop = true }
opentelemetry-instrumentation-chromadb = { path = "../opentelemetry-instrumentation-chromadb", develop = true }
sqlalchemy = "^2.0.31"
llama-index-agent-openai = "^0.4.1"
llama-index-vector-stores-chroma = "^0.4.1"
llama-index-llms-cohere = "^0.4.0"
llama-index-embeddings-openai = "^0.3.1"
onnxruntime = "<1.20.0"
llama-parse = "^0.5.20"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.extras]
instruments = ["llama-index"]
llamaparse = ["llama-parse"]

[tool.poetry.plugins."opentelemetry_instrumentor"]
llama-index = "opentelemetry.instrumentation.llamaindex:LlamaIndexInstrumentor"
