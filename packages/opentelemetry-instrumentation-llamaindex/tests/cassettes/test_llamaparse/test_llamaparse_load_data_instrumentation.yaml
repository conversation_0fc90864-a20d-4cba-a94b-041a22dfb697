interactions:
- request:
    body: from_python_package=true&input_url=https%3A%2F%2Farxiv.org%2Fpdf%2F1706.03762.pdf&language=en&parsing_instruction=
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate, br
      connection:
      - keep-alive
      content-length:
      - '114'
      content-type:
      - application/x-www-form-urlencoded
      host:
      - api.cloud.llamaindex.ai
      user-agent:
      - python-httpx/0.27.2
    method: POST
    uri: https://api.cloud.llamaindex.ai/api/parsing/upload
  response:
    body:
      string: '{"id":"18a0509c-a0a9-41f5-abbe-7177d041aa7c","status":"PENDING"}'
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '64'
      Content-Type:
      - application/json
      Date:
      - Thu, 10 Jul 2025 19:48:07 GMT
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      x-correlation-id:
      - 6af3d456-cca9-49f7-8854-5fc674fcef9c
      x-session-id:
      - 05b65a93-7a68-49f8-b0e7-1f2b79799cee
    status:
      code: 200
      message: OK
- request:
    body: ''
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate, br
      connection:
      - keep-alive
      host:
      - api.cloud.llamaindex.ai
      user-agent:
      - python-httpx/0.27.2
    method: GET
    uri: https://api.cloud.llamaindex.ai/api/parsing/job/18a0509c-a0a9-41f5-abbe-7177d041aa7c
  response:
    body:
      string: '{"id":"18a0509c-a0a9-41f5-abbe-7177d041aa7c","status":"PENDING"}'
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '64'
      Content-Type:
      - application/json
      Date:
      - Thu, 10 Jul 2025 19:48:08 GMT
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      x-correlation-id:
      - a495f41a-cb45-40b6-b8a4-cc95e1ec89d5
      x-session-id:
      - 607e9854-6c6e-43a6-bd62-557ee3a4c48d
    status:
      code: 200
      message: OK
- request:
    body: ''
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate, br
      connection:
      - keep-alive
      host:
      - api.cloud.llamaindex.ai
      user-agent:
      - python-httpx/0.27.2
    method: GET
    uri: https://api.cloud.llamaindex.ai/api/parsing/job/18a0509c-a0a9-41f5-abbe-7177d041aa7c
  response:
    body:
      string: '{"id":"18a0509c-a0a9-41f5-abbe-7177d041aa7c","status":"SUCCESS"}'
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '64'
      Content-Type:
      - application/json
      Date:
      - Thu, 10 Jul 2025 19:48:10 GMT
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      x-correlation-id:
      - 8a2aeab0-2e75-4f4f-a1ed-1817654ebe08
      x-session-id:
      - d465f81c-36c3-46a4-9dbf-7402c6e4608a
    status:
      code: 200
      message: OK
- request:
    body: ''
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate, br
      connection:
      - keep-alive
      host:
      - api.cloud.llamaindex.ai
      user-agent:
      - python-httpx/0.27.2
    method: GET
    uri: https://api.cloud.llamaindex.ai/api/parsing/job/18a0509c-a0a9-41f5-abbe-7177d041aa7c/result/text
  response:
    body:
      string: "{\"text\":\"                                arXiv:1706.03762v7  [cs.CL]
        \ 2 Aug 2023\\n                            Provided proper attribution is
        provided, Google hereby grants permission to\\n                          reproduce
        the tables and figures in this paper solely for use in journalistic or\\n
        \                                        scholarly works.\\n                          Attention
        Is All You Need\\n      Ashish Vaswani\u2217            Noam Shazeer\u2217
        \           Niki Parmar\u2217         Jakob Uszkoreit\u2217\\n             Google
        Brain          Google Brain          Google Research        Google Research\\n
        \        <EMAIL>     <EMAIL>         <EMAIL>
        \       <EMAIL>\\n         Llion Jones\u2217              Aidan N.
        Gomez\u2217 \u2020                    \u0141ukasz Kaiser\u2217\\n         Google
        Research          University of Toronto                  Google Brain\\n         <EMAIL>
        \         <EMAIL>            <EMAIL>\\n                                         Illia
        Polosukhin\u2217 \u2021\\n                                                     <EMAIL>\\n
        \                                                             Abstract\\n
        \                           The dominant sequence transduction models are
        based on complex recurrent or\\n                           convolutional neural
        networks that include an encoder and a decoder. The best\\n                            performing
        models also connect the encoder and decoder through an attention\\n         mechanism.
        We propose a new simple network architecture, the Transformer,\\n                         based
        solely on attention mechanisms, dispensing with recurrence and convolutions\\n
        \        entirely. Experiments on two machine translation tasks show these
        models to\\n                         be superior in quality while being more
        parallelizable and requiring significantly\\n         less time to train.
        Our model achieves 28.4 BLEU on the WMT 2014 English-\\n                          to-German
        translation task, improving over the existing best results, including\\n         ensembles,
        by over 2 BLEU. On the WMT 2014 English-to-French translation task,\\n                         our
        model establishes a new single-model state-of-the-art BLEU score of 41.8 after\\n
        \                        training for 3.5 days on eight GPUs, a small fraction
        of the training costs of the\\n                         best models from the
        literature. We show that the Transformer generalizes well to\\n                         other
        tasks by applying it successfully to English constituency parsing both with\\n
        \        large and limited training data.\\n   \u2217Equal contribution. Listing
        order is random. Jakob proposed replacing RNNs with self-attention and started\\n
        \         the effort to evaluate this idea. Ashish, with Illia, designed and
        implemented the first Transformer models and\\n          has been crucially
        involved in every aspect of this work. Noam proposed scaled dot-product attention,
        multi-head\\n           attention and the parameter-free position representation
        and became the other person involved in nearly every\\n           detail.
        Niki designed, implemented, tuned and evaluated countless model variants in
        our original codebase and\\n          tensor2tensor. Llion also experimented
        with novel model variants, was responsible for our initial codebase, and\\n
        \        efficient inference and visualizations. Lukasz and Aidan spent countless
        long days designing various parts of and\\n          implementing tensor2tensor,
        replacing our earlier codebase, greatly improving results and massively accelerating\\nour
        research.\\n   \u2020Work performed while at Google Brain.\\n   \u2021Work
        performed while at Google Research.\\n31st Conference on Neural Information
        Processing Systems (NIPS 2017), Long Beach, CA, USA.\\n---\\n1   Introduction\\nRecurrent
        neural networks, long short-term memory [13] and gated recurrent [7] neural
        networks\\nin particular, have been firmly established as state of the art
        approaches in sequence modeling and\\ntransduction problems such as language
        modeling and machine translation [35, 2, 5]. Numerous\\nefforts have since
        continued to push the boundaries of recurrent language models and encoder-decoder\\narchitectures
        [38, 24, 15].\\nRecurrent models typically factor computation along the symbol
        positions of the input and output\\nsequences. Aligning the positions to steps
        in computation time, they generate a sequence of hidden\\nstates ht, as a
        function of the previous hidden state ht\u22121 and the input for position
        t. This inherently\\nsequential nature precludes parallelization within training
        examples, which becomes critical at longer\\nsequence lengths, as memory constraints
        limit batching across examples. Recent work has achieved\\nsignificant improvements
        in computational efficiency through factorization tricks [21] and conditional\\ncomputation
        [32], while also improving model performance in case of the latter. The fundamental\\nconstraint
        of sequential computation, however, remains.\\nAttention mechanisms have become
        an integral part of compelling sequence modeling and transduc-\\ntion models
        in various tasks, allowing modeling of dependencies without regard to their
        distance in\\nthe input or output sequences [2, 19]. In all but a few cases
        [27], however, such attention mechanisms\\nare used in conjunction with a
        recurrent network.\\nIn this work we propose the Transformer, a model architecture
        eschewing recurrence and instead\\nrelying entirely on an attention mechanism
        to draw global dependencies between input and output.\\nThe Transformer allows
        for significantly more parallelization and can reach a new state of the art
        in\\ntranslation quality after being trained for as little as twelve hours
        on eight P100 GPUs.\\n2   Background\\nThe goal of reducing sequential computation
        also forms the foundation of the Extended Neural GPU\\n[16], ByteNet [18]
        and ConvS2S [9], all of which use convolutional neural networks as basic building\\nblock,
        computing hidden representations in parallel for all input and output positions.
        In these models,\\nthe number of operations required to relate signals from
        two arbitrary input or output positions grows\\nin the distance between positions,
        linearly for ConvS2S and logarithmically for ByteNet. This makes\\nit more
        difficult to learn dependencies between distant positions [12]. In the Transformer
        this is\\nreduced to a constant number of operations, albeit at the cost of
        reduced effective resolution due\\nto averaging attention-weighted positions,
        an effect we counteract with Multi-Head Attention as\\ndescribed in section
        3.2.\\nSelf-attention, sometimes called intra-attention is an attention mechanism
        relating different positions\\nof a single sequence in order to compute a
        representation of the sequence. Self-attention has been\\nused successfully
        in a variety of tasks including reading comprehension, abstractive summarization,\\ntextual
        entailment and learning task-independent sentence representations [4, 27,
        28, 22].\\nEnd-to-end memory networks are based on a recurrent attention mechanism
        instead of sequence-\\naligned recurrence and have been shown to perform well
        on simple-language question answering and\\nlanguage modeling tasks [34].\\nTo
        the best of our knowledge, however, the Transformer is the first transduction
        model relying\\nentirely on self-attention to compute representations of its
        input and output without using sequence-\\naligned RNNs or convolution. In
        the following sections, we will describe the Transformer, motivate\\nself-attention
        and discuss its advantages over models such as [17, 18] and [9].\\n3   Model
        Architecture\\nMost competitive neural sequence transduction models have an
        encoder-decoder structure [5, 2, 35].\\nHere, the encoder maps an input sequence
        of symbol representations (x1, ..., xn) to a sequence\\nof continuous representations
        z   = (z1, ..., zn). Given z, the decoder then generates an output\\nsequence
        (y1, ..., ym) of symbols one element at a time. At each step the model is
        auto-regressive\\n[10], consuming the previously generated symbols as additional
        input when generating the next.\\n                                                   2\\n---\\n
        \                                                       Output\\n                                                    Probabilities\\n
        \                                                      Softmax\\n                                                        Linear\\n
        \                                                     Add & Norm\\n                                                         Feed\\n
        \                                                       Forward\\n                                   Add
        & Norm         Add & Norm\\n                                      Feed            Multi-Head\\n
        \                                   Forward             Attention       Nx\\n
        \                       Nx         Add & Norm         Add & Norm\\n                                   Multi-Head
        \          Masked\\n                                                      Multi-Head\\n
        \                                  Attention            Attention\\n                      Positional
        \                                    Positional\\n                      Encoding
        \                                      Encoding\\n                                      Input
        \            Output\\n                                   Embedding          Embedding\\n
        \                                    Inputs            Outputs\\n                                                   (shifted
        right)\\n                          Figure 1: The Transformer - model architecture.\\nThe
        Transformer follows this overall architecture using stacked self-attention
        and point-wise, fully\\nconnected layers for both the encoder and decoder,
        shown in the left and right halves of Figure 1,\\nrespectively.\\n3.1   Encoder
        and Decoder Stacks\\nEncoder:     The encoder is composed of a stack of N
        \    = 6 identical layers. Each layer has two\\nsub-layers. The first is a
        multi-head self-attention mechanism, and the second is a simple, position-\\nwise
        fully connected feed-forward network. We employ a residual connection [11]
        around each of\\nthe two sub-layers, followed by layer normalization [1].
        That is, the output of each sub-layer is\\nLayerNorm(x + Sublayer(x)), where
        Sublayer(x) is the function implemented by the sub-layer\\nitself. To facilitate
        these residual connections, all sub-layers in the model, as well as the embedding\\nlayers,
        produce outputs of dimension dmodel = 512.\\nDecoder:    The decoder is also
        composed of a stack of N = 6 identical layers. In addition to the two\\nsub-layers
        in each encoder layer, the decoder inserts a third sub-layer, which performs
        multi-head\\nattention over the output of the encoder stack. Similar to the
        encoder, we employ residual connections\\naround each of the sub-layers, followed
        by layer normalization. We also modify the self-attention\\nsub-layer in the
        decoder stack to prevent positions from attending to subsequent positions.
        This\\nmasking, combined with fact that the output embeddings are offset by
        one position, ensures that the\\npredictions for position i can depend only
        on the known outputs at positions less than i.\\n3.2   Attention\\nAn attention
        function can be described as mapping a query and a set of key-value pairs
        to an output,\\nwhere the query, keys, values, and output are all vectors.
        The output is computed as a weighted sum\\n                                                 3\\n---\\n
        \          Scaled Dot-Product Attention                         Multi-Head
        Attention\\n                                                                          Linear\\n
        \                       MatMul\\n                   SoftMax                                                Concat\\n
        \                Mask (opt )                                    Scaled Dot-Product\\n
        \                                                                         Attention\\n
        \                   Scale\\n                    MatMul                                   Linear
        \      Linear   Linear\\n                                                                          K\\nFigure
        2: (left) Scaled Dot-Product Attention. (right) Multi-Head Attention consists
        of several\\nattention layers running in parallel.\\nof the values, where
        the weight assigned to each value is computed by a compatibility function
        of the\\nquery with the corresponding key.\\n3.2.1      Scaled Dot-Product
        Attention\\nWe call our particular attention \\\"Scaled Dot-Product Attention\\\"
        (Figure 2). The input consists of\\nqueries and keys of dimension d     ,
        and values of dimension d        . We compute the dot products of the\\nquery
        with all keys, divide each k   \u221A                       v\\nvalues.                         by
        \           dk, and apply a softmax function to obtain the weights on the\\nIn
        practice, we compute the attention function on a set of queries simultaneously,
        packed together\\ninto a matrix Q. The keys and values are also packed together
        into matrices K and V . We compute\\nthe matrix of outputs as:\\n                                                                QK
        T\\n                            Attention(Q, K, V ) = softmax( \u221Adk )V
        \                              (1)\\nThe two most commonly used attention
        functions are additive attention [2], and dot-product (multi-\\nplicative)
        attention. Dot-product attention is identical to our algorithm, except for
        the scaling factor\\nof  1            . Additive attention computes the compatibility
        function using a feed-forward network with\\n    \u221Adk\\na single hidden
        layer. While the two are similar in theoretical complexity, dot-product attention
        is\\nmuch faster and more space-efficient in practice, since it can be implemented
        using highly optimized\\nmatrix multiplication code.\\nWhile for small values
        of dk the two mechanisms perform similarly, additive attention outperforms\\ndot
        product attention without scaling for larger values of dk [3]. We suspect
        that for large values of\\ndk, the dot products grow large in magnitude, pushing
        the softmax function into regions where it has\\nextremely small gradients
        4. To counteract this effect, we scale the dot products by     1 .\\n                                                                                      \u221Adk\\n3.2.2
        \     Multi-Head Attention\\nInstead of performing a single attention function
        with dmodel-dimensional keys, values and queries,\\nwe found it beneficial
        to linearly project the queries, keys and values h times with different, learned\\nlinear
        projections to dk, dk and dv dimensions, respectively. On each of these projected
        versions of\\nqueries, keys and values we then perform the attention function
        in parallel, yielding dv-dimensional\\n4To illustrate why the dot products
        get large, assume that the components of q and k are independent random\\nvariables
        with mean 0 and variance 1. Then their dot product, q \xB7 k = P\u1D48\u1D4F
        qiki, has mean 0 and variance d\u2096.\\n                                                                 i=1\\n
        \                                                4\\n---\\noutput values.
        These are concatenated and once again projected, resulting in the final values,
        as\\ndepicted in Figure 2.\\nMulti-head attention allows the model to jointly
        attend to information from different representation\\nsubspaces at different
        positions. With a single attention head, averaging inhibits this.\\n                     MultiHead(Q,
        K, V ) = Concat(head1, ..., headh)W O\\n                             where
        headi = Attention(QW Q, KWK, V W V )\\n                                                             i
        \      i      i\\nWhere the projections are parameter matrices W Q \u2208
        Rdmodel\xD7d\u2096, W K \u2208 Rdmodel\xD7d\u2096, W V    \u2208 Rdmodel\xD7dv\\nand
        W O \u2208 Rhdv\xD7dmodel .                        i                  i                 i\\nIn
        this work we employ h     = 8 parallel attention layers, or heads.     For
        each of these we use\\ndk = dv = dmodel/h = 64. Due to the reduced dimension
        of each head, the total computational cost\\nis similar to that of single-head
        attention with full dimensionality.\\n3.2.3  Applications of Attention in
        our Model\\nThe Transformer uses multi-head attention in three different ways:\\n
        \      \u2022  In \\\"encoder-decoder attention\\\" layers, the queries come
        from the previous decoder layer,\\n          and the memory keys and values
        come from the output of the encoder. This allows every\\n          position
        in the decoder to attend over all positions in the input sequence. This mimics
        the\\n          typical encoder-decoder attention mechanisms in sequence-to-sequence
        models such as\\n          [38, 2, 9].\\n       \u2022   The encoder contains
        self-attention layers. In a self-attention layer all of the keys, values\\n
        \         and queries come from the same place, in this case, the output of
        the previous layer in the\\n          encoder. Each position in the encoder
        can attend to all positions in the previous layer of the\\n          encoder.\\n
        \      \u2022  Similarly, self-attention layers in the decoder allow each
        position in the decoder to attend to\\n          all positions in the decoder
        up to and including that position. We need to prevent leftward\\n          information
        flow in the decoder to preserve the auto-regressive property. We implement
        this\\n          inside of scaled dot-product attention by masking out (setting
        to \u2212\u221E) all values in the input\\n          of the softmax which
        correspond to illegal connections. See Figure 2.\\n3.3    Position-wise Feed-Forward
        Networks\\nIn addition to attention sub-layers, each of the layers in our
        encoder and decoder contains a fully\\nconnected feed-forward network, which
        is applied to each position separately and identically. This\\nconsists of
        two linear transformations with a ReLU activation in between.\\n                              FFN(x)
        = max(0, xW1 + b1)W2 + b2                                  (2)\\n While the
        linear transformations are the same across different positions, they use different
        parameters\\nfrom layer to layer.  Another way of describing this is as two
        convolutions with kernel size 1.\\nThe dimensionality of input and output
        is dmodel    = 512, and the inner-layer has dimensionality\\ndf f = 2048.\\n3.4
        \   Embeddings and Softmax\\nSimilarly to other sequence transduction models,
        we use learned embeddings to convert the input\\ntokens and output tokens
        to vectors of dimension dmodel. We also use the usual learned linear transfor-\\nmation
        and softmax function to convert the decoder output to predicted next-token
        probabilities. In\\nour model, we share the same weight matrix between the
        two embedding layers and the pre-softmax\\nlinear transformation, similar
        to [30]. In the embedding layers, we multiply those weights by \u221Admodel.\\n
        \                                                   5\\n---\\nTable 1: Maximum
        path lengths, per-layer complexity and minimum number of sequential operations\\nfor
        different layer types. n is the sequence length, d is the representation dimension,
        k is the kernel\\nsize of convolutions and r the size of the neighborhood
        in restricted self-attention.\\n     Layer Type                   Complexity
        per Layer    Sequential     Maximum Path Length\\n                                                          Operations\\n
        \    Self-Attention                    O(n2 \xB7 d)             O(1)                O(1)\\n
        \    Recurrent                         O(n \xB7 d2)             O(n)                O(n)\\n
        \    Convolutional                   O(k \xB7 n \xB7 d2)           O(1)             O(logk(n))\\n
        \    Self-Attention (restricted)      O(r \xB7 n \xB7 d)           O(1)               O(n/r)\\n3.5
        \  Positional Encoding\\nSince our model contains no recurrence and no convolution,
        in order for the model to make use of the\\norder of the sequence, we must
        inject some information about the relative or absolute position of the\\ntokens
        in the sequence. To this end, we add \\\"positional encodings\\\" to the input
        embeddings at the\\nbottoms of the encoder and decoder stacks. The positional
        encodings have the same dimension dmodel\\nas the embeddings, so that the
        two can be summed. There are many choices of positional encodings,\\nlearned
        and fixed [9].\\nIn this work, we use sine and cosine functions of different
        frequencies:\\n                                    P E(pos,2i) = sin(pos/100002i/d\u1D50\u1D52\u1D48\u1D49\u02E1
        )\\n                                  P E(pos,2i+1) = cos(pos/100002i/d\u1D50\u1D52\u1D48\u1D49\u02E1
        )\\nwhere pos is the position and i is the dimension. That is, each dimension
        of the positional encoding\\ncorresponds to a sinusoid. The wavelengths form
        a geometric progression from 2\u03C0 to 10000 \xB7 2\u03C0. We\\nchose this
        function because we hypothesized it would allow the model to easily learn
        to attend by\\nrelative positions, since for any fixed offset k, P Epos+k
        can be represented as a linear function of\\nP Epos.\\n     We also experimented
        with using learned positional embeddings [9] instead, and found that the two\\nversions
        produced nearly identical results (see Table 3 row (E)). We chose the sinusoidal
        version\\nbecause it may allow the model to extrapolate to sequence lengths
        longer than the ones encountered\\nduring training.\\n4    Why Self-Attention\\nIn
        this section we compare various aspects of self-attention layers to the recurrent
        and convolu-\\ntional layers commonly used for mapping one variable-length
        sequence of symbol representations\\n(x1, ..., xn) to another sequence of
        equal length (z1, ..., zn), with xi, zi \u2208 Rd, such as a hidden\\nlayer
        in a typical sequence transduction encoder or decoder. Motivating our use
        of self-attention we\\nconsider three desiderata.\\nOne is the total computational
        complexity per layer. Another is the amount of computation that can\\nbe parallelized,
        as measured by the minimum number of sequential operations required.\\nThe
        third is the path length between long-range dependencies in the network. Learning
        long-range\\ndependencies is a key challenge in many sequence transduction
        tasks. One key factor affecting the\\nability to learn such dependencies is
        the length of the paths forward and backward signals have to\\ntraverse in
        the network. The shorter these paths between any combination of positions
        in the input\\nand output sequences, the easier it is to learn long-range
        dependencies [12]. Hence we also compare\\nthe maximum path length between
        any two input and output positions in networks composed of the\\ndifferent
        layer types.\\nAs noted in Table 1, a self-attention layer connects all positions
        with a constant number of sequentially\\nexecuted operations, whereas a recurrent
        layer requires O(n) sequential operations. In terms of\\ncomputational complexity,
        self-attention layers are faster than recurrent layers when the sequence\\n
        \                                                    6\\n---\\nlength n is
        smaller than the representation dimensionality d, which is most often the
        case with\\nsentence representations used by state-of-the-art models in machine
        translations, such as word-piece\\n[38] and byte-pair [31] representations.
        To improve computational performance for tasks involving\\nvery long sequences,
        self-attention could be restricted to considering only a neighborhood of size
        r in\\nthe input sequence centered around the respective output position.
        This would increase the maximum\\npath length to O(n/r). We plan to investigate
        this approach further in future work.\\nA single convolutional layer with
        kernel width k < n does not connect all pairs of input and output\\npositions.
        Doing so requires a stack of O(n/k) convolutional layers in the case of contiguous
        kernels,\\nor O(logk(n)) in the case of dilated convolutions [18], increasing
        the length of the longest paths\\nbetween any two positions in the network.
        Convolutional layers are generally more expensive than\\nrecurrent layers,
        by a factor of k. Separable convolutions [6], however, decrease the complexity\\nconsiderably,
        to O(k \xB7 n \xB7 d + n \xB7 d2). Even with k = n, however, the complexity
        of a separable\\nconvolution is equal to the combination of a self-attention
        layer and a point-wise feed-forward layer,\\nthe approach we take in our model.\\nAs
        side benefit, self-attention could yield more interpretable models. We inspect
        attention distributions\\nfrom our models and present and discuss examples
        in the appendix. Not only do individual attention\\nheads clearly learn to
        perform different tasks, many appear to exhibit behavior related to the syntactic\\nand
        semantic structure of the sentences.\\n5    Training\\nThis section describes
        the training regime for our models.\\n5.1   Training Data and Batching\\nWe
        trained on the standard WMT 2014 English-German dataset consisting of about
        4.5 million\\nsentence pairs. Sentences were encoded using byte-pair encoding
        [3], which has a shared source-\\ntarget vocabulary of about 37000 tokens.
        For English-French, we used the significantly larger WMT\\n2014 English-French
        dataset consisting of 36M sentences and split tokens into a 32000 word-piece\\nvocabulary
        [38]. Sentence pairs were batched together by approximate sequence length.
        Each training\\nbatch contained a set of sentence pairs containing approximately
        25000 source tokens and 25000\\ntarget tokens.\\n5.2   Hardware and Schedule\\nWe
        trained our models on one machine with 8 NVIDIA P100 GPUs. For our base models
        using\\nthe hyperparameters described throughout the paper, each training
        step took about 0.4 seconds. We\\ntrained the base models for a total of 100,000
        steps or 12 hours. For our big models,(described on the\\nbottom line of table
        3), step time was 1.0 seconds. The big models were trained for 300,000 steps\\n(3.5
        days).\\n5.3   Optimizer\\nWe used the Adam optimizer [20] with \u03B21 =
        0.9, \u03B22 = 0.98 and \u03F5 = 10\u22129. We varied the learning\\nrate
        over the course of training, according to the formula:\\n              lrate
        = d\u22120.5 \xB7 min(step_num\u22120.5, step_num \xB7 warmup_steps\u22121.5)
        \                   (3)\\n                        model\\nThis corresponds
        to increasing the learning rate linearly for the first warmup_steps training
        steps,\\nand decreasing it thereafter proportionally to the inverse square
        root of the step number. We used\\nwarmup_steps = 4000.\\n5.4   Regularization\\nWe
        employ three types of regularization during training:\\n                                                 7\\n---\\nTable
        2: The Transformer achieves better BLEU scores than previous state-of-the-art
        models on the\\nEnglish-to-German and English-to-French newstest2014 tests
        at a fraction of the training cost.\\n       Model                                       BLEU
        \             Training Cost (FLOPs)\\n                                             EN-DE
        \     EN-FR          EN-DE        EN-FR\\n       ByteNet [18]                           23.75\\n
        \      Deep-Att + PosUnk [39]                            39.2                       1.0
        \xB7 1020\\n       GNMT + RL [38]                          24.6     39.92
        \       2.3 \xB7 1019     1.4 \xB7 1020\\n       ConvS2S [9]                            25.16
        \    40.46        9.6 \xB7 1018     1.5 \xB7 1020\\n       MoE [32]                               26.03
        \    40.56        2.0 \xB7 1019     1.2 \xB7 1020\\n       Deep-Att + PosUnk
        Ensemble [39]                   40.4                       8.0 \xB7 1020\\n
        \      GNMT + RL Ensemble [38]                26.30     41.16        1.8 \xB7
        1020     1.1 \xB7 1021\\n       ConvS2S Ensemble [9]                   26.36
        \    41.29        7.7 \xB7 1019     1.2 \xB7 1021\\n       Transformer (base
        model)                27.3      38.1              3.3 \xB7 1018\\n       Transformer
        (big)                       28.4      41.8              2.3 \xB7 1019\\nResidual
        Dropout      We apply dropout [33] to the output of each sub-layer, before
        it is added to the\\nsub-layer input and normalized. In addition, we apply
        dropout to the sums of the embeddings and the\\npositional encodings in both
        the encoder and decoder stacks. For the base model, we use a rate of\\nPdrop
        = 0.1.\\nLabel Smoothing             During training, we employed label smoothing
        of value \u03F5ls = 0.1 [36]. This\\nhurts perplexity, as the model learns
        to be more unsure, but improves accuracy and BLEU score.\\n6    Results\\n6.1
        \  Machine Translation\\nOn the WMT 2014 English-to-German translation task,
        the big transformer model (Transformer (big)\\nin Table 2) outperforms the
        best previously reported models (including ensembles) by more than 2.0\\nBLEU,
        establishing a new state-of-the-art BLEU score of 28.4. The configuration
        of this model is\\nlisted in the bottom line of Table 3. Training took 3.5
        days on 8 P100 GPUs. Even our base model\\nsurpasses all previously published
        models and ensembles, at a fraction of the training cost of any of\\nthe competitive
        models.\\nOn the WMT 2014 English-to-French translation task, our big model
        achieves a BLEU score of 41.0,\\noutperforming all of the previously published
        single models, at less than 1/4 the training cost of the\\nprevious state-of-the-art
        model. The Transformer (big) model trained for English-to-French used\\ndropout
        rate Pdrop = 0.1, instead of 0.3.\\nFor the base models, we used a single
        model obtained by averaging the last 5 checkpoints, which\\nwere written at
        10-minute intervals. For the big models, we averaged the last 20 checkpoints.
        We\\nused beam search with a beam size of 4 and length penalty \u03B1 = 0.6
        [38]. These hyperparameters\\nwere chosen after experimentation on the development
        set. We set the maximum output length during\\ninference to input length +
        50, but terminate early when possible [38].\\nTable 2 summarizes our results
        and compares our translation quality and training costs to other model\\narchitectures
        from the literature. We estimate the number of floating point operations used
        to train a\\nmodel by multiplying the training time, the number of GPUs used,
        and an estimate of the sustained\\nsingle-precision floating-point capacity
        of each GPU 5.\\n6.2   Model Variations\\nTo evaluate the importance of different
        components of the Transformer, we varied our base model\\nin different ways,
        measuring the change in performance on English-to-German translation on the\\n
        \    5We used values of 2.8, 3.7, 6.0 and 9.5 TFLOPS for K80, K40, M40 and
        P100, respectively.\\n                                                 8\\n---\\n
        \                         Table 3: Variations on the Transformer architecture.
        Unlisted values are identical to those of the base\\nmodel. All metrics are
        on the English-to-German translation development set, newstest2013. Listed\\nperplexities
        are per-wordpiece, according to our byte-pair encoding, and should not be
        compared to\\nper-word perplexities.\\n          N     dmodel    dff     h
        \    dk     dv    Pdrop    \u03F5ls    train     PPL     BLEU   params\\n
        \                                                                    steps
        \ (dev)     (dev)    \xD7106\\n  base     6      512     2048    8     64
        \    64     0.1     0.1     100K     4.92    25.8     65\\n                                  1
        \   512    512                              5.29    24.9\\n  (A)                             4
        \   128    128                              5.00    25.5\\n                                  16
        \   32     32                              4.91    25.8\\n                                  32
        \   16     16                              5.01    25.4\\n  (B)                                   16
        \                                    5.16    25.1     58\\n                                        32
        \                                    5.01    25.4     60\\n           2                                                                   6.11
        \   23.7     36\\n           4                                                                   5.19
        \   25.3     50\\n           8                                                                   4.88
        \   25.5     80\\n   (C)            256                   32     32                              5.75
        \   24.5     28\\n                 1024                  128    128                              4.66
        \   26.0    168\\n                          1024                                                 5.12
        \   25.4     53\\n                          4096                                                 4.75
        \   26.2     90\\n                                                      0.0
        \                     5.77    24.6\\n  (D)                                                 0.2
        \    0.0              4.95    25.5\\n                                                                               4.67
        \   25.3\\n                                                              0.2
        \             5.47    25.7\\n  (E)             positional embedding instead
        of sinusoids                    4.92    25.7\\n  big      6     1024     4096
        \   16              0.3           300K           4.33    26.4   213\\ndevelopment
        set, newstest2013. We used beam search as described in the previous section,
        but no\\ncheckpoint averaging. We present these results in Table 3.\\nIn Table
        3 rows (A), we vary the number of attention heads and the attention key and
        value dimensions,\\nkeeping the amount of computation constant, as described
        in Section 3.2.2.             While single-head\\nattention is 0.9 BLEU worse
        than the best setting, quality also drops off with too many heads.\\nIn Table
        3 rows (B), we observe that reducing the attention key size dk hurts model
        quality. This\\nsuggests that determining compatibility is not easy and that
        a more sophisticated compatibility\\nfunction than dot product may be beneficial.
        We further observe in rows (C) and (D) that, as expected,\\nbigger models
        are better, and dropout is very helpful in avoiding over-fitting. In row (E)
        we replace our\\nsinusoidal positional encoding with learned positional embeddings
        [9], and observe nearly identical\\nresults to the base model.\\n6.3   English
        Constituency Parsing\\nTo evaluate if the Transformer can generalize to other
        tasks we performed experiments on English\\nconstituency parsing. This task
        presents specific challenges: the output is subject to strong structural\\nconstraints
        and is significantly longer than the input. Furthermore, RNN sequence-to-sequence\\nmodels
        have not been able to attain state-of-the-art results in small-data regimes
        [37].\\nWe trained a 4-layer transformer with dmodel = 1024 on the Wall Street
        Journal (WSJ) portion of the\\nPenn Treebank [25], about 40K training sentences.
        We also trained it in a semi-supervised setting,\\nusing the larger high-confidence
        and BerkleyParser corpora from with approximately 17M sentences\\n[37]. We
        used a vocabulary of 16K tokens for the WSJ only setting and a vocabulary
        of 32K tokens\\nfor the semi-supervised setting.\\nWe performed only a small
        number of experiments to select the dropout, both attention and residual\\n(section
        5.4), learning rates and beam size on the Section 22 development set, all
        other parameters\\nremained unchanged from the English-to-German base translation
        model. During inference, we\\n                                                                             9\\n---\\nTable
        4: The Transformer generalizes well to English constituency parsing (Results
        are on Section 23\\n of WSJ)\\n                         Parser                          Training
        \           WSJ 23 F1\\n          Vinyals & Kaiser el al. (2014) [37]    WSJ
        only, discriminative       88.3\\n               Petrov et al. (2006) [29]
        \        WSJ only, discriminative       90.4\\n                 Zhu et al.
        (2013) [40]          WSJ only, discriminative       90.4\\n                 Dyer
        et al. (2016) [8]          WSJ only, discriminative       91.7\\n                 Transformer
        (4 layers)          WSJ only, discriminative       91.3\\n                 Zhu
        et al. (2013) [40]              semi-supervised            91.3\\n               Huang
        & Harper (2009) [14]            semi-supervised            91.3\\n              McClosky
        et al. (2006) [26]            semi-supervised            92.1\\n          Vinyals
        & Kaiser el al. (2014) [37]        semi-supervised            92.1\\n                 Transformer
        (4 layers)              semi-supervised            92.7\\n                Luong
        et al. (2015) [23]                multi-task              93.0\\n                 Dyer
        et al. (2016) [8]                 generative              93.3\\n increased
        the maximum output length to input length + 300. We used a beam size of 21
        and \u03B1 = 0.3\\n for both WSJ only and the semi-supervised setting.\\n
        Our results in Table 4 show that despite the lack of task-specific tuning
        our model performs sur-\\n prisingly well, yielding better results than all
        previously reported models with the exception of the\\n Recurrent Neural Network
        Grammar [8].\\n In contrast to RNN sequence-to-sequence models [37], the Transformer
        outperforms the Berkeley-\\n Parser [29] even when training only on the WSJ
        training set of 40K sentences.\\n 7    Conclusion\\n In this work, we presented
        the Transformer, the first sequence transduction model based entirely on\\n
        attention, replacing the recurrent layers most commonly used in encoder-decoder
        architectures with\\n multi-headed self-attention.\\n For translation tasks,
        the Transformer can be trained significantly faster than architectures based\\n
        on recurrent or convolutional layers.    On both WMT 2014 English-to-German
        and WMT 2014\\n English-to-French translation tasks, we achieve a new state
        of the art. In the former task our best\\n model outperforms even all previously
        reported ensembles.\\nWe are excited about the future of attention-based models
        and plan to apply them to other tasks. We\\n plan to extend the Transformer
        to problems involving input and output modalities other than text and\\n to
        investigate local, restricted attention mechanisms to efficiently handle large
        inputs and outputs\\n such as images, audio and video. Making generation less
        sequential is another research goals of ours.\\nThe code we used to train
        and evaluate our models is available at         https://github.com/\\n tensorflow/tensor2tensor.\\n
        Acknowledgements       We are grateful to Nal Kalchbrenner and Stephan Gouws
        for their fruitful\\n comments, corrections and inspiration.\\n References\\n
        \ [1]  Jimmy Lei Ba, Jamie Ryan Kiros, and Geoffrey E Hinton. Layer normalization.
        arXiv preprint\\n       arXiv:1607.06450, 2016.\\n  [2]  Dzmitry Bahdanau,
        Kyunghyun Cho, and Yoshua Bengio. Neural machine translation by jointly\\n
        \      learning to align and translate. CoRR, abs/1409.0473, 2014.\\n  [3]
        \ Denny Britz, Anna Goldie, Minh-Thang Luong, and Quoc V. Le. Massive exploration
        of neural\\n       machine translation architectures. CoRR, abs/1703.03906,
        2017.\\n  [4]  Jianpeng Cheng, Li Dong, and Mirella Lapata. Long short-term
        memory-networks for machine\\n       reading. arXiv preprint arXiv:1601.06733,
        2016.\\n                                                10\\n---\\n [5]  Kyunghyun
        Cho, Bart van Merrienboer, Caglar Gulcehre, Fethi Bougares, Holger Schwenk,\\n
        \     and Yoshua Bengio. Learning phrase representations using rnn encoder-decoder
        for statistical\\n      machine translation. CoRR, abs/1406.1078, 2014.\\n
        [6]  Francois Chollet. Xception: Deep learning with depthwise separable convolutions.
        \  arXiv\\n      preprint arXiv:1610.02357, 2016.\\n [7]  Junyoung Chung,
        \xC7aglar G\xFCl\xE7ehre, Kyunghyun Cho, and Yoshua Bengio. Empirical evaluation\\n
        \     of gated recurrent neural networks on sequence modeling. CoRR, abs/1412.3555,
        2014.\\n [8]  Chris Dyer, Adhiguna Kuncoro, Miguel Ballesteros, and Noah A.
        Smith. Recurrent neural\\n      network grammars. In Proc. of NAACL, 2016.\\n
        [9]  Jonas Gehring, Michael Auli, David Grangier, Denis Yarats, and Yann N.
        Dauphin. Convolu-\\n      tional sequence to sequence learning. arXiv preprint
        arXiv:1705.03122v2, 2017.\\n[10]  Alex Graves.     Generating sequences with
        recurrent neural networks.    arXiv preprint\\n      arXiv:1308.0850, 2013.\\n[11]
        \ Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning
        for im-\\n      age recognition.  In Proceedings of the IEEE Conference on
        Computer Vision and Pattern\\n      Recognition, pages 770\u2013778, 2016.\\n[12]
        \ Sepp Hochreiter, Yoshua Bengio, Paolo Frasconi, and J\xFCrgen Schmidhuber.
        Gradient flow in\\n      recurrent nets: the difficulty of learning long-term
        dependencies, 2001.\\n[13]  Sepp Hochreiter and J\xFCrgen Schmidhuber. Long
        short-term memory.         Neural computation,\\n      9(8):1735\u20131780,
        1997.\\n[14]  Zhongqiang Huang and Mary Harper. Self-training PCFG grammars
        with latent annotations\\n      across languages. In Proceedings of the 2009
        Conference on Empirical Methods in Natural\\n      Language Processing, pages
        832\u2013841. ACL, August 2009.\\n[15]  Rafal Jozefowicz, Oriol Vinyals, Mike
        Schuster, Noam Shazeer, and Yonghui Wu. Exploring\\n      the limits of language
        modeling. arXiv preprint arXiv:1602.02410, 2016.\\n[16]  \u0141ukasz Kaiser
        and Samy Bengio. Can active memory replace attention? In Advances in Neural\\n
        \     Information Processing Systems, (NIPS), 2016.\\n[17]  \u0141ukasz Kaiser
        and Ilya Sutskever. Neural GPUs learn algorithms. In International Conference\\n
        \     on Learning Representations (ICLR), 2016.\\n[18]  Nal Kalchbrenner,
        Lasse Espeholt, Karen Simonyan, Aaron van den Oord, Alex Graves, and Ko-\\n
        \     ray Kavukcuoglu. Neural machine translation in linear time. arXiv preprint
        arXiv:1610.10099v2,\\n      2017.\\n[19]  Yoon Kim, Carl Denton, Luong Hoang,
        and Alexander M. Rush. Structured attention networks.\\n      In International
        Conference on Learning Representations, 2017.\\n[20]  Diederik Kingma and
        Jimmy Ba. Adam: A method for stochastic optimization. In ICLR, 2015.\\n[21]
        \ Oleksii Kuchaiev and Boris Ginsburg. Factorization tricks for LSTM networks.
        arXiv preprint\\n      arXiv:1703.10722, 2017.\\n[22]  Zhouhan Lin, Minwei
        Feng, Cicero Nogueira dos Santos, Mo Yu, Bing Xiang, Bowen\\n      Zhou, and
        Yoshua Bengio. A structured self-attentive sentence embedding.  arXiv preprint\\n
        \     arXiv:1703.03130, 2017.\\n[23]  Minh-Thang Luong, Quoc V. Le, Ilya Sutskever,
        Oriol Vinyals, and Lukasz Kaiser. Multi-task\\n      sequence to sequence
        learning. arXiv preprint arXiv:1511.06114, 2015.\\n[24]  Minh-Thang Luong,
        Hieu Pham, and Christopher D Manning. Effective approaches to attention-\\n
        \     based neural machine translation. arXiv preprint arXiv:1508.04025, 2015.\\n
        \                                             11\\n---\\n[25]  Mitchell P
        Marcus, Mary Ann Marcinkiewicz, and Beatrice Santorini. Building a large annotated\\n
        \     corpus of english: The penn treebank. Computational linguistics, 19(2):313\u2013330,
        1993.\\n[26]  David McClosky, Eugene Charniak, and Mark Johnson. Effective
        self-training for parsing. In\\n      Proceedings of the Human Language Technology
        Conference of the NAACL, Main Conference,\\n      pages 152\u2013159. ACL,
        June 2006.\\n[27]  Ankur Parikh, Oscar T\xE4ckstr\xF6m, Dipanjan Das, and
        Jakob Uszkoreit. A decomposable attention\\n      model. In Empirical Methods
        in Natural Language Processing, 2016.\\n[28]  Romain Paulus, Caiming Xiong,
        and Richard Socher. A deep reinforced model for abstractive\\n      summarization.
        arXiv preprint arXiv:1705.04304, 2017.\\n[29]  Slav Petrov, Leon Barrett,
        Romain Thibaux, and Dan Klein.   Learning accurate, compact,\\n      and interpretable
        tree annotation. In Proceedings of the 21st International Conference on\\n
        \     Computational Linguistics and 44th Annual Meeting of the ACL, pages
        433\u2013440. ACL, July\\n      2006.\\n[30]  Ofir Press and Lior Wolf. Using
        the output embedding to improve language models.     arXiv\\n      preprint
        arXiv:1608.05859, 2016.\\n[31]  Rico Sennrich, Barry Haddow, and Alexandra
        Birch. Neural machine translation of rare words\\n      with subword units.
        arXiv preprint arXiv:1508.07909, 2015.\\n[32]  Noam Shazeer, Azalia Mirhoseini,
        Krzysztof Maziarz, Andy Davis, Quoc Le, Geoffrey Hinton,\\n      and Jeff
        Dean. Outrageously large neural networks: The sparsely-gated mixture-of-experts\\n
        \     layer. arXiv preprint arXiv:1701.06538, 2017.\\n[33]  Nitish Srivastava,
        Geoffrey E Hinton, Alex Krizhevsky, Ilya Sutskever, and Ruslan Salakhutdi-\\n
        \     nov. Dropout: a simple way to prevent neural networks from overfitting.
        Journal of Machine\\n      Learning Research, 15(1):1929\u20131958, 2014.\\n[34]
        \ Sainbayar Sukhbaatar, Arthur Szlam, Jason Weston, and Rob Fergus. End-to-end
        memory\\n      networks. In C. Cortes, N. D. Lawrence, D. D. Lee, M. Sugiyama,
        and R. Garnett, editors,\\n      Advances in Neural Information Processing
        Systems 28, pages 2440\u20132448. Curran Associates,\\n      Inc., 2015.\\n[35]
        \ Ilya Sutskever, Oriol Vinyals, and Quoc VV Le. Sequence to sequence learning
        with neural\\n      networks. In Advances in Neural Information Processing
        Systems, pages 3104\u20133112, 2014.\\n[36]  Christian Szegedy, Vincent Vanhoucke,
        Sergey Ioffe, Jonathon Shlens, and Zbigniew Wojna.\\n      Rethinking the
        inception architecture for computer vision. CoRR, abs/1512.00567, 2015.\\n[37]
        \ Vinyals & Kaiser, Koo, Petrov, Sutskever, and Hinton. Grammar as a foreign
        language. In\\n      Advances in Neural Information Processing Systems, 2015.\\n[38]
        \ Yonghui Wu, Mike Schuster, Zhifeng Chen, Quoc V Le, Mohammad Norouzi, Wolfgang\\n
        \     Macherey, Maxim Krikun, Yuan Cao, Qin Gao, Klaus Macherey, et al. Google\u2019s
        neural machine\\n      translation system: Bridging the gap between human
        and machine translation. arXiv preprint\\n      arXiv:1609.08144, 2016.\\n[39]
        \ Jie Zhou, Ying Cao, Xuguang Wang, Peng Li, and Wei Xu.      Deep recurrent
        models with\\n      fast-forward connections for neural machine translation.
        CoRR, abs/1606.04199, 2016.\\n[40]  Muhua Zhu, Yue Zhang, Wenliang Chen, Min
        Zhang, and Jingbo Zhu.        Fast and accurate\\n      shift-reduce constituent
        parsing. In Proceedings of the 51st Annual Meeting of the ACL (Volume\\n     1:
        Long Papers), pages 434\u2013443. ACL, August 2013.\\n                                              12\\n---\\nInput-Input
        Layer5\\nAttention Visualizations\\n                                                  governments
        \                      registration\\n                                             American
        \                                     process\\n                                      majority
        \          passed    making                            difficult<EOS>           <pad>\\n
        \                                                                                                               <pad>
        \        <pad>\\n                                                                                                                <pad>
        <pad>\\n                                                                   since
        \                  voting more                   <pad>\\n                    spirit
        \                       have           laws 2009\\n   It     is    in  this
        \    thata     of                new                 the     or                      .\\n
        \  It     is        this     thata     of                                    the
        \    or                      .  <EOS>\\n                                                                                                     difficult\\n
        \               in  spirit                                                                           more
        \               <pad>\\n                                                                   since
        \                  process              <pad>\\n                                                                 laws
        \                                          <pad> <pad> <pad>\\n                                                  have
        \  new                               voting                             <pad>\\n
        \                                            American\\n                                      majority
        \          passed          2009\\n                                                                            making\\n
        \                                                 governments                       registration\\nFigure
        3: An example of the attention mechanism following long-distance dependencies
        in the\\nencoder self-attention in layer 5 of 6. Many of the attention heads
        attend to a distant dependency of\\nthe verb \u2018making\u2019, completing
        the phrase \u2018making...more difficult\u2019. Attentions here shown only
        for\\nthe word \u2018making\u2019. Different colors represent different heads.
        Best viewed in color.\\n                                                                         13\\n---\\nInput-Input
        Layer5\\n                                       application\\n                                                                                                     missing
        \                   <EOS>\\n       Law    never         perfect               should
        \                            what                               opinion   <pad>\\n
        \   The    will    be       ,          butits      be just-            thisis
        \        we are          ,     in    my      .\\nInput-Input Layer5                                                                                                        .
        \    <EOS>\\n    The                     ,          its                       -
        \    this                           ,\\n                   be perfectbut                   be
        just                     is    what     are           in    my\\n       Law
        \      never                            should                              we
        \            missing                      <pad>\\n           will                                  application
        \                                                           opinion\\n                                                  application\\n
        \                                                                                                    missing
        \                    <EOS>\\n       Law         never perfect                   should
        \                            what                               opinion  <pad>\\n
        \   The    will     be      ,          but its      be just-            thisis
        \       we are           ,    in     my        .\\n    The                     ,
        \         its                       -      this                           ,
        \                    .   <EOS>\\n                    be perfectbut                   be
        just                    is     what    are           in     my\\n       Law
        \        never                           should                             we
        \            missing                      <pad>\\n           will                                   application
        \                                                           opinion\\nFigure
        4: Two attention heads, also in layer 5 of 6, apparently involved in anaphora
        resolution. Top:\\nFull attentions for head 5. Bottom: Isolated attentions
        from just the word \u2018its\u2019 for attention heads 5\\nand 6. Note that
        the attentions are very sharp for this word.\\n                                                                 14\\n---\\nInput-Input
        Layer5\\n                                               application\\n                                                                                 missing
        \                    <EOS>\\n      Law           never    perfect            should
        \                     what                         opinion  <pad>\\n    The
        \   will       be     ,    but its        be just-           thisis  we are
        \ ,       in     my     .\\n    The                      ,          its                   -
        \    this            ,                     .  <EOS>\\n                      be
        perfectbut              be just            is      what    are     in     my\\n
        \     Law           never                       should                      we
        \  missing                          <pad>\\n           will                                application
        \                                              opinion\\nInput-Input Layer5\\n
        \                                              application\\n                                                                                 missing
        \                    <EOS>\\n      Law          never     perfect            should
        \                     what                         opinion  <pad>\\n   The
        \    will      be      ,    but its        be just-           thisis  we are
        \ ,       in     my     .\\n   The                       ,          its                   -
        \    this            ,                     .  <EOS>\\n                     be
        perfectbut               be just            is      what    are     in     my\\n
        \     Law          never                        should                      we
        \  missing                          <pad>\\n           will                                application
        \                                              opinion\\nFigure 5: Many of
        the attention heads exhibit behaviour that seems related to the structure
        of the\\nsentence. We give two such examples above, from two different heads
        from the encoder self-attention\\nat layer 5 of 6. The heads clearly learned
        to perform different tasks.\\n                                                              15\",\"job_metadata\":{\"credits_used\":0,\"job_credits_usage\":0,\"job_pages\":0,\"job_auto_mode_triggered_pages\":0,\"job_is_cache_hit\":true}}"
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '52875'
      Content-Type:
      - application/json
      Date:
      - Thu, 10 Jul 2025 19:48:11 GMT
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      x-correlation-id:
      - b5a60013-7abd-47a4-a8c9-d3fc8206ba90
      x-session-id:
      - a57f9433-af60-4b0e-ab58-298fbdbd8a2b
    status:
      code: 200
      message: OK
version: 1
