[tool.coverage.run]
branch = true
source = ["sample_app"]

[tool.coverage.report]
exclude_lines = ["if TYPE_CHECKING:"]
show_missing = true

[tool.poetry]
name = "sample-app"
version = "0.0.1"
description = "Sample Application using Traceloop SDK"
authors = [
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
]
license = "Apache-2.0"
readme = "README.md"

[[tool.poetry.packages]]
include = "sample_app"

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
openai = "^1.52.2"
requests = "^2.32.3"
# pinecone-datasets = "^0.7.0"
pinecone-client = "^3.2.2"
llama-index = "^0.12.6"
duckduckgo-search = "^3.9.11"
chromadb = "^0.5.15"
tokenizers = "~0.20.1"
boto3 = "^1.35.49"
transformers = "^4.46.0"
replicate = "^0.22.0"
cohere = "^5.11.1"
anthropic = "^0.37.1"
google-cloud-aiplatform = "^1.81.0"
python-dotenv = "^1.0.1"
langchain = "^0.3.24"
langchain-community = "^0.3.22"
haystack-ai = "~2.6.1"
datasets = "~2.21.0"
llama-index-embeddings-huggingface = "^0.4.0"
litellm = "^1.51.0"
text-generation = "^0.7.0"
llama-index-vector-stores-chroma = "^0.4.1"
langchain-openai = "^0.3.14"
google-generativeai = "^0.8.3"
langgraph = "^0.2.39"
groq = "^0.11.0"
llama-index-llms-openai = "^0.3.12"
ibm-watson-machine-learning = "^1.0.367"
ollama = "^0.4.7"
mcp = "^1.7.1"
openai-agents = "^0.0.19"


[tool.poetry.dependencies.opentelemetry-instrumentation-openai]
path = "../opentelemetry-instrumentation-openai"
develop = true

[tool.poetry.dependencies.opentelemetry-instrumentation-haystack]
path = "../opentelemetry-instrumentation-haystack"
develop = true

[tool.poetry.dependencies.opentelemetry-instrumentation-pinecone]
path = "../opentelemetry-instrumentation-pinecone"
develop = true

[tool.poetry.dependencies.opentelemetry-instrumentation-replicate]
path = "../opentelemetry-instrumentation-replicate"
develop = true

[tool.poetry.dependencies.opentelemetry-instrumentation-vertexai]
path = "../opentelemetry-instrumentation-vertexai"
develop = true

[tool.poetry.dependencies.opentelemetry-instrumentation-google-generativeai]
path = "../opentelemetry-instrumentation-google-generativeai"
develop = true

[tool.poetry.dependencies.opentelemetry-instrumentation-groq]
path = "../opentelemetry-instrumentation-groq"
develop = true

[tool.poetry.dependencies.traceloop-sdk]
path = "../traceloop-sdk"
develop = true

[tool.poetry.dependencies.opentelemetry-instrumentation-crewai]
path = "../opentelemetry-instrumentation-crewai"
develop = true

[tool.poetry.dependencies.opentelemetry-instrumentation-openai-agents]
path = "../opentelemetry-instrumentation-openai-agents"
develop = true

[tool.poetry.group.dev.dependencies]
autopep8 = "^2.3.1"
flake8 = "7.1.1"
pytest = "^8.3.3"
pytest-sugar = "1.0.0"
pytest-cov = "4.1.0"
pytest-html = "4.1.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
