[tool.poetry]
name = "opentelemetry-instrumentation-watsonx"
version = "0.43.1"
description = "OpenTelemetry IBM Watsonx Instrumentation"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
repository = "https://github.com/traceloop/openllmetry/tree/main/packages/opentelemetry-instrumentation-watsonx"
license = "Apache-2.0"
readme = "README.md"

[[tool.poetry.packages]]
include = "opentelemetry/instrumentation/watsonx"

[tool.poetry.dependencies]
python = ">=3.9,<4"
opentelemetry-api = "^1.28.0"
opentelemetry-instrumentation = ">=0.50b0"
opentelemetry-semantic-conventions = ">=0.50b0"
opentelemetry-semantic-conventions-ai = "0.4.11"

[tool.poetry.group.dev.dependencies]
autopep8 = "^2.2.0"
flake8 = "7.0.0"
pytest = "^8.2.2"
pytest-sugar = "1.0.0"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-sugar = "1.0.0"
vcrpy = "^6.0.1"
pytest-recording = "^0.13.1"
opentelemetry-sdk = "^1.27.0"
pytest-asyncio = "^0.23.7"
ibm-watson-machine-learning = "1.0.333"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.extras]
instruments = ["ibm-watson-machine-learning"]

[tool.poetry.plugins."opentelemetry_instrumentor"]
ibm-watson-machine-learning = "opentelemetry.instrumentation.watsonx:WatsonxInstrumentor"
