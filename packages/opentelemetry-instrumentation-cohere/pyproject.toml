[tool.coverage.run]
branch = true
source = ["opentelemetry/instrumentation/cohere"]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.poetry]
name = "opentelemetry-instrumentation-cohere"
version = "0.40.14"
description = "OpenTelemetry Cohere instrumentation"
authors = [
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
]
repository = "https://github.com/traceloop/openllmetry/tree/main/packages/opentelemetry-instrumentation-cohere"
license = "Apache-2.0"
readme = "README.md"

[[tool.poetry.packages]]
include = "opentelemetry/instrumentation/cohere"

[tool.poetry.dependencies]
python = ">=3.9,<4"
opentelemetry-api = "^1.28.0"
opentelemetry-instrumentation = ">=0.50b0"
opentelemetry-semantic-conventions = ">=0.50b0"
opentelemetry-semantic-conventions-ai = "0.4.10"

[tool.poetry.group.dev.dependencies]
autopep8 = "^2.2.0"
flake8 = "7.0.0"
pytest = "^8.2.2"
pytest-sugar = "1.0.0"

[tool.poetry.group.test.dependencies]
cohere = "^5.3.3"
pytest = "^8.2.2"
pytest-sugar = "1.0.0"
vcrpy = "^6.0.1"
pytest-recording = "^0.13.1"
opentelemetry-sdk = "^1.27.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.extras]
instruments = ["cohere"]

[tool.poetry.plugins."opentelemetry_instrumentor"]
cohere = "opentelemetry.instrumentation.cohere:CohereInstrumentor"
