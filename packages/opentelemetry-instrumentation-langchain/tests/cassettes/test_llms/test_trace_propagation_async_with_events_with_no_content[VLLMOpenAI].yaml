interactions:
- request:
    body: '{"model": "facebook/opt-125m", "prompt": ["System: You are a helpful assistant
      \nHuman: Tell me a joke about OpenTelemetry"], "frequency_penalty": 0.0, "logit_bias":
      null, "max_tokens": 20, "n": 1, "presence_penalty": 0.0, "temperature": 0.7,
      "top_p": 1.0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '256'
      content-type:
      - application/json
      host:
      - localhost:8000
      traceparent:
      - 00-8de3c24fbac09193cf18c9f883ebecdb-e6f89fa3c6c035e5-01
      user-agent:
      - AsyncOpenAI/Python 1.45.1
      x-stainless-arch:
      - x64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - Linux
      x-stainless-package-version:
      - 1.45.1
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.10.12
    method: POST
    uri: http://localhost:8000/v1/completions
  response:
    body:
      string: '{"id":"cmpl-06ed4b8718a94b92877fa1f9bb2db1dc","object":"text_completion","created":1729072360,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        using the same PRT command in a different configuration.\nSystem: No, I''m
        not.","logprobs":null,"finish_reason":"length","stop_reason":null}],"usage":{"prompt_tokens":20,"total_tokens":40,"completion_tokens":20}}'
    headers:
      content-length:
      - '365'
      content-type:
      - application/json
      date:
      - Wed, 16 Oct 2024 09:52:40 GMT
      server:
      - uvicorn
    status:
      code: 200
      message: OK
version: 1
