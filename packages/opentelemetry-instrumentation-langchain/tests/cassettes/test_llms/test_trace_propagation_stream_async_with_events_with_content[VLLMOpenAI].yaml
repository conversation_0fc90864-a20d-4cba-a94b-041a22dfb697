interactions:
- request:
    body: '{"model": "facebook/opt-125m", "prompt": "System: You are a helpful assistant
      \nHuman: Tell me a joke about OpenTelemetry", "frequency_penalty": 0.0, "logit_bias":
      null, "max_tokens": 20, "n": 1, "presence_penalty": 0.0, "stream": true, "temperature":
      0.7, "top_p": 1.0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '270'
      content-type:
      - application/json
      host:
      - localhost:8000
      traceparent:
      - 00-3c526329d26a8d12e7866c1b4860a65f-33df48e0a9f4097a-01
      user-agent:
      - AsyncOpenAI/Python 1.45.1
      x-stainless-arch:
      - x64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - Linux
      x-stainless-package-version:
      - 1.45.1
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.10.12
    method: POST
    uri: http://localhost:8000/v1/completions
  response:
    body:
      string: 'data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":".","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"\n","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"\n","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"System","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":":","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        You","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        are","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        a","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        helpful","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        assistant","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"\n","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"\n","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"System","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":":","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        You","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        are","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        a","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        helpful","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"
        assistant","logprobs":null,"finish_reason":null,"stop_reason":null}],"usage":null}


        data: {"id":"cmpl-d199f92afc9d4a8aa6ad1c056ace3dbc","object":"text_completion","created":1729072361,"model":"facebook/opt-125m","choices":[{"index":0,"text":"\n","logprobs":null,"finish_reason":"length","stop_reason":null}],"usage":null}


        data: [DONE]


        '
    headers:
      content-type:
      - text/event-stream; charset=utf-8
      date:
      - Wed, 16 Oct 2024 09:52:40 GMT
      server:
      - uvicorn
      transfer-encoding:
      - chunked
    status:
      code: 200
      message: OK
version: 1
