interactions:
- request:
    body: '{"model": "facebook/opt-125m", "prompt": ["System: You are a helpful assistant
      \nHuman: Tell me a joke about OpenTelemetry"], "frequency_penalty": 0.0, "logit_bias":
      null, "max_tokens": 20, "n": 1, "presence_penalty": 0.0, "temperature": 0.7,
      "top_p": 1.0}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '256'
      content-type:
      - application/json
      host:
      - localhost:8000
      traceparent:
      - 00-a1133fb0ea7804a989c16e8e55dbf935-33ef37fc99e8f410-01
      user-agent:
      - OpenAI/Python 1.45.1
      x-stainless-arch:
      - x64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - Linux
      x-stainless-package-version:
      - 1.45.1
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.10.12
    method: POST
    uri: http://localhost:8000/v1/completions
  response:
    body:
      string: '{"id":"cmpl-d04f0bea50ce4db38349934fd667823e","object":"text_completion","created":1729072359,"model":"facebook/opt-125m","choices":[{"index":0,"text":"\n\nTelemetry
        is an important part of the Open Telemetry community.\n\nPeople who are","logprobs":null,"finish_reason":"length","stop_reason":null}],"usage":{"prompt_tokens":20,"total_tokens":40,"completion_tokens":20}}'
    headers:
      content-length:
      - '371'
      content-type:
      - application/json
      date:
      - Wed, 16 Oct 2024 09:52:39 GMT
      server:
      - uvicorn
    status:
      code: 200
      message: OK
version: 1
