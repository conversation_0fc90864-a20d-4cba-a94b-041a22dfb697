interactions:
- request:
    body: '{"include":[],"input":[{"content":"Please handle this task by delegating
      to another agent.","role":"user"}],"instructions":"You decide which agent to
      handoff to.","model":"gpt-4.1","stream":false,"tools":[{"name":"handoff_to_agent_a","parameters":{"properties":{"input":{"title":"Input","type":"string"}},"required":["input"],"title":"handoff_to_agent_a_args","type":"object","additionalProperties":false},"strict":true,"type":"function","description":"Handoff
      to Agent A for specific tasks"},{"name":"handoff_to_agent_b","parameters":{"properties":{"input":{"title":"Input","type":"string"}},"required":["input"],"title":"handoff_to_agent_b_args","type":"object","additionalProperties":false},"strict":true,"type":"function","description":"Handoff
      to Agent B for different tasks"},{"name":"transfer_to_agenta","parameters":{"additionalProperties":false,"type":"object","properties":{},"required":[]},"strict":true,"type":"function","description":"Handoff
      to the AgentA agent to handle the request. "},{"name":"transfer_to_agentb","parameters":{"additionalProperties":false,"type":"object","properties":{},"required":[]},"strict":true,"type":"function","description":"Handoff
      to the AgentB agent to handle the request. "}]}'
    headers:
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1223'
      cookie:
      - __cf_bm=3zyMrBe7N20pVl8x25O_On5.fvfIikDA012ufvx53nM-1751862660-*******-JvGF1w1po85PLKWFrJyLhImrIM6CpkmMaYGua6WBvJlfDMgZo9Oigg5nRJtYzI47T1BQnozcg9CowCmBpXifPOG5KiUOD.Kr9VCCCeladCY;
        _cfuvid=O..E2LqPai_CvoCODzw4ICRfn1jA4LzcFld6LdnWH1s-1751862660981-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - Agents/Python 0.0.19
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.93.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.13.1
    method: POST
    uri: https://api.openai.com/v1/responses
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA+RWS4/bNhC++1cQOu8uJFu2Zd92D3kgOQRpiyYoFsKIHMrMUqJKDrfZLvzfA1Gy
        HvtoF2gRIMjFkOfjvL5v+LhfMBYpEe1ZZNE1+SbbFKnI5LLI4izZQbFKcSVW220Rb0TCl3GapSg3
        6yRdys2ar6KzNoApviCnUxBTO+zs3CIQihxaLNmuk2yz3GyTgDkC8q714aZqNBKKzqkAflNa4+u2
        KgnaYTCjtcZGe1Z7rYNB1SfHXCCB0m6OOrKekzJ1SPLZeCaQK4Hsr4PiBwYl1sTIsAPUwkjJyFx0
        BVTwNTeeGk85mRusZ3FbkIzROQc9z1gZgbpNVTZ0nl4k58t4uT6P0/Mk7VkKMaM9+2PBGGP34Xeg
        X/KBfL7O0pb83W6zzHbrjAu5itNd/CT5IQTdNRiC+Dq0HKob4ee4DiDY0ldYU8DvjyPQxsi74sLn
        u99/+fVT9Uq9v01/K6++/H3zsTF369fp6FFDFcogC7WTaHMyeeAZorDkuGDsOnDRgAWtUc+5JOs7
        sRuLt8p4l5/mqatj4NoiOFOruoz2PY8RSmksTRa1ffuqAnvXGxeMHbvRQ3urOOaksJ2oSKAErynq
        59JYnNZCWDVogXwwJxdxb/1KY3JpbAXj/4kiYd3QfJe/6/lgFA9LwJOJBsA9HpCH6o6EC3TcqiYY
        9yx6M4wyuwzjfcmkscw1yJVUnBG4G/dYrn4HDGrlMK5phaqQ0LpJd0Eh06AlhXN72HndlE+NbROK
        dMj2NuBnD9C+RUe2VXUCHofv4+gTWfzTK4tiIGuWfLBcT1yGAh63m4MtXTRd25fTH2wTBIRQLd2g
        P0wZCOfU4kGdoZtwLrbDtJhg/4O0V0FaoaREG06yF2pb/FzaFj+WtnTATt/L+QWlMUAtNejogr3o
        zP1Xof+p4RcxNp+U50S8/t7kXf1n8oofi7zhXiXT5NqUjTVFGzUejM307rK+5nAiUCgHhT69vryD
        EseLLey58RmUrHdnj4HJ++t+fDzwA4rRM56qHD18XiWrp4Cn4g43/3OhyRDoSeTtcrh3vcPZC7JC
        AgEEgf7F8RsAAAD//wMAY6ngegwLAAA=
    headers:
      CF-RAY:
      - 95b49c5db9254448-BOM
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 07 Jul 2025 04:31:11 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - traceloop
      openai-processing-ms:
      - '716'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999624'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_22e4c6df41b5e7e4195bcdc050043339
    status:
      code: 200
      message: OK
- request:
    body: '{"include":[],"input":[{"content":"Please handle this task by delegating
      to another agent.","role":"user"},{"arguments":"{}","call_id":"call_KWSTXmFiLv4UgBjzkRpoy5G4","name":"transfer_to_agenta","type":"function_call","id":"fc_686b4d8fc584819a99628958cdf304900484ef65142f65c3","status":"completed"},{"call_id":"call_KWSTXmFiLv4UgBjzkRpoy5G4","output":"{\"assistant\":
      \"AgentA\"}","type":"function_call_output"}],"instructions":"Agent A does something.","model":"gpt-4.1","stream":false,"tools":[]}'
    headers:
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '498'
      cookie:
      - __cf_bm=3zyMrBe7N20pVl8x25O_On5.fvfIikDA012ufvx53nM-1751862660-*******-JvGF1w1po85PLKWFrJyLhImrIM6CpkmMaYGua6WBvJlfDMgZo9Oigg5nRJtYzI47T1BQnozcg9CowCmBpXifPOG5KiUOD.Kr9VCCCeladCY;
        _cfuvid=O..E2LqPai_CvoCODzw4ICRfn1jA4LzcFld6LdnWH1s-1751862660981-*******-604800000
      host:
      - api.openai.com
      user-agent:
      - Agents/Python 0.0.19
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - async:asyncio
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.93.0
      x-stainless-read-timeout:
      - '600'
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.13.1
    method: POST
    uri: https://api.openai.com/v1/responses
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA3RUTXObQAy951eonG0POGCwbzn23lumwwhWYOplRXe1aTIZ//cOi41N61z8oSc9
        SU/Sfj4BRJ2KDhBZckO5K3ZVqvbxc7ZNi2SP+7zJC8zyZpvs9wlhnBYpNbssSbfNLqufo9VIwNUv
        quVKwsbRZK8toZAqccSSPEuK3XaXbwPmBMW7MabmftAkpKagCutTa9mbsaoGtaNgJmvZRgcwXutg
        6Mw1sFQk2Gm3RJ1YX0vHJiR5ackIvIBicuC4Jzl2pt1MKXt8L9nL4KUUPpFZMI2gMOuyRr3M0bMi
        PZK3g6zTTbLexttsHafrJL3oEjijA7w+AQB8hs9Z8N61s95FnsSj3lWmEkqTPKYY02pXPNQ7cMjH
        QIGFnMOWbsBXwgawZiNkbiXdl7WgvcpB7zJHBwc0hgWvur7+XICa28Fy9QAJRAeIfhwJBN0Jjuig
        IjKgSFM7rgkIw3VMDVs4olG6M+0K0IGl356ckNrA9wY+2IMhUjA1C35QKOSALQyWGrKAhuU4fo+E
        Kxg0oSPQJNATnAz/+RbN5Z0vv+aKI8s6qIDOdU7QyOQ8OganaECLWpNeboZYPy3rYOmtY+/K6z2U
        YeTz5lhCx6YzbXS4yB9R07CVO6dxlL7v0X5cjE8A5+l0yL51NZXS0XgRkaIGvZ7mFDlhS/e1CPUD
        WRQfzMkmvljDPC7JG7Y93v7f7UHwm5uf8k89H7mrJ5G8cDQDt9lHwkN5txHxbBzuC7He1GGfQiud
        w0pfnwIfNnuusjOLC83y1f/2u7dg7qXG+kjqFhhP/Vyi/z385/gR8Ih3nuJX1MKC+gYW+Syhd7R4
        zHoSVCg40p+fzn8BAAD//wMAARM9UZcFAAA=
    headers:
      CF-RAY:
      - 95b49c6449964448-BOM
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Mon, 07 Jul 2025 04:31:13 GMT
      Server:
      - cloudflare
      Transfer-Encoding:
      - chunked
      X-Content-Type-Options:
      - nosniff
      alt-svc:
      - h3=":443"; ma=86400
      cf-cache-status:
      - DYNAMIC
      openai-organization:
      - traceloop
      openai-processing-ms:
      - '849'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      x-ratelimit-limit-requests:
      - '10000'
      x-ratelimit-limit-tokens:
      - '30000000'
      x-ratelimit-remaining-requests:
      - '9999'
      x-ratelimit-remaining-tokens:
      - '29999923'
      x-ratelimit-reset-requests:
      - 6ms
      x-ratelimit-reset-tokens:
      - 0s
      x-request-id:
      - req_66d7d10bcb6956e38ffc4272297e87d4
    status:
      code: 200
      message: OK
version: 1
