{"name": "opentelemetry-instrumentation-openai-agents", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "packages/opentelemetry-instrumentation-openai-agents/opentelemetry/instrumentation/openai_agents", "targets": {"lock": {"executor": "@nxlv/python:run-commands", "options": {"command": "poetry lock", "cwd": "packages/opentelemetry-instrumentation-openai-agents"}}, "add": {"executor": "@nxlv/python:add", "options": {}}, "update": {"executor": "@nxlv/python:update", "options": {}}, "remove": {"executor": "@nxlv/python:remove", "options": {}}, "build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "packages/opentelemetry-instrumentation-openai-agents/dist", "publish": false, "lockedVersions": true, "bundleLocalDependencies": true}}, "install": {"executor": "@nxlv/python:install", "options": {"silent": false, "args": "", "cacheDir": ".cache/pypoetry", "verbose": false, "debug": false}}, "lint": {"executor": "@nxlv/python:flake8", "outputs": ["{workspaceRoot}/reports/packages/opentelemetry-instrumentation-openai-agents/pylint.txt"], "options": {"outputFile": "reports/packages/opentelemetry-instrumentation-openai-agents/pylint.txt"}}, "test": {"executor": "@nxlv/python:run-commands", "outputs": ["{workspaceRoot}/reports/packages/opentelemetry-instrumentation-openai-agents/unittests", "{workspaceRoot}/coverage/packages/opentelemetry-instrumentation-openai-agents"], "options": {"command": "poetry run pytest tests/", "cwd": "packages/opentelemetry-instrumentation-openai-agents"}}, "build-release": {"executor": "@nxlv/python:run-commands", "options": {"commands": ["chmod +x ../../scripts/build-release.sh", "../../scripts/build-release.sh"], "cwd": "packages/opentelemetry-instrumentation-openai-agents"}}}, "tags": ["instrumentation"]}