"""
Tests for GenAI Entry Detection functionality.

This module tests the GenAI entry span detection and marking functionality
that was added to identify entry points for GenAI operations.
"""

import os
import pytest
from opentelemetry.semconv_ai import SpanAttributes
from opentelemetry.semconv_ai.genai_entry import (
    GENAI_ENTRY_ATTRIBUTE,
    GENAI_ENTRY_ENV_VAR,
    is_genai_entry_enabled,
    mark_span_as_genai_entry,
)


class TestGenAIEntryDetection:
    """Test cases for GenAI entry detection functionality."""

    def test_genai_entry_env_var_enabled_by_default(self):
        """Test that GenAI entry detection is enabled by default."""
        # Clear any existing env var
        original_value = os.environ.pop(GENAI_ENTRY_ENV_VAR, None)
        try:
            assert is_genai_entry_enabled() is True
        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value

    def test_genai_entry_env_var_can_be_disabled(self):
        """Test that GenAI entry detection can be disabled via env var."""
        original_value = os.environ.get(GENAI_ENTRY_ENV_VAR)
        try:
            os.environ[GENAI_ENTRY_ENV_VAR] = "false"
            assert is_genai_entry_enabled() is False
        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value
            else:
                os.environ.pop(GENAI_ENTRY_ENV_VAR, None)

    def test_genai_entry_env_var_case_insensitive(self):
        """Test that GenAI entry detection env var is case insensitive."""
        original_value = os.environ.get(GENAI_ENTRY_ENV_VAR)
        try:
            for value in ["TRUE", "True", "true", "FALSE", "False", "false"]:
                os.environ[GENAI_ENTRY_ENV_VAR] = value
                expected = value.lower() == "true"
                assert is_genai_entry_enabled() is expected
        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value
            else:
                os.environ.pop(GENAI_ENTRY_ENV_VAR, None)

    @pytest.mark.vcr
    def test_chat_completion_has_genai_entry_attribute(
        self, instrument_legacy, span_exporter, openai_client
    ):
        """Test that chat completion spans are marked with GenAI entry attribute."""
        # Ensure GenAI entry detection is enabled
        original_value = os.environ.get(GENAI_ENTRY_ENV_VAR)
        try:
            os.environ[GENAI_ENTRY_ENV_VAR] = "true"

            openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "user", "content": "Hello, test message for GenAI entry detection"}],
                max_tokens=10
            )

            spans = span_exporter.get_finished_spans()
            assert len(spans) == 1

            chat_span = spans[0]
            assert chat_span.name == "openai.chat"

            # Check that the span has the GenAI entry attribute
            assert chat_span.attributes.get(GENAI_ENTRY_ATTRIBUTE) is True

        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value
            else:
                os.environ.pop(GENAI_ENTRY_ENV_VAR, None)

    @pytest.mark.vcr
    def test_chat_completion_no_genai_entry_when_disabled(
        self, instrument_legacy, span_exporter, openai_client
    ):
        """Test that GenAI entry attribute is not added when detection is disabled."""
        original_value = os.environ.get(GENAI_ENTRY_ENV_VAR)
        try:
            os.environ[GENAI_ENTRY_ENV_VAR] = "false"

            openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "user", "content": "Hello, test message for disabled GenAI entry"}],
                max_tokens=10
            )

            spans = span_exporter.get_finished_spans()
            assert len(spans) == 1

            chat_span = spans[0]
            assert chat_span.name == "openai.chat"

            # Check that the span does NOT have the GenAI entry attribute
            assert GENAI_ENTRY_ATTRIBUTE not in chat_span.attributes

        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value
            else:
                os.environ.pop(GENAI_ENTRY_ENV_VAR, None)

    @pytest.mark.vcr
    async def test_async_chat_completion_has_genai_entry_attribute(
        self, instrument_legacy, span_exporter, async_openai_client
    ):
        """Test that async chat completion spans are marked with GenAI entry attribute."""
        original_value = os.environ.get(GENAI_ENTRY_ENV_VAR)
        try:
            os.environ[GENAI_ENTRY_ENV_VAR] = "true"

            await async_openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "user", "content": "Hello, async test message"}],
                max_tokens=10
            )

            spans = span_exporter.get_finished_spans()
            assert len(spans) == 1

            chat_span = spans[0]
            assert chat_span.name == "openai.chat"

            # Check that the span has the GenAI entry attribute
            assert chat_span.attributes.get(GENAI_ENTRY_ATTRIBUTE) is True

        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value
            else:
                os.environ.pop(GENAI_ENTRY_ENV_VAR, None)

    @pytest.mark.vcr
    def test_streaming_chat_completion_has_genai_entry_attribute(
        self, instrument_legacy, span_exporter, openai_client
    ):
        """Test that streaming chat completion spans are marked with GenAI entry attribute."""
        original_value = os.environ.get(GENAI_ENTRY_ENV_VAR)
        try:
            os.environ[GENAI_ENTRY_ENV_VAR] = "true"

            stream = openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Hello, streaming test"}],
                max_tokens=10,
                stream=True
            )

            # Consume the stream
            for chunk in stream:
                pass

            spans = span_exporter.get_finished_spans()
            assert len(spans) == 1

            chat_span = spans[0]
            assert chat_span.name == "openai.chat"

            # Check that the span has the GenAI entry attribute
            assert chat_span.attributes.get(GENAI_ENTRY_ATTRIBUTE) is True

        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value
            else:
                os.environ.pop(GENAI_ENTRY_ENV_VAR, None)

    @pytest.mark.vcr
    async def test_async_streaming_chat_completion_has_genai_entry_attribute(
        self, instrument_legacy, span_exporter, async_openai_client
    ):
        """Test that async streaming chat completion spans are marked with GenAI entry attribute."""
        original_value = os.environ.get(GENAI_ENTRY_ENV_VAR)
        try:
            os.environ[GENAI_ENTRY_ENV_VAR] = "true"

            stream = await async_openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "user", "content": "Hello, async streaming test for GenAI entry"}],
                max_tokens=10,
                stream=True
            )

            # Consume the async stream
            async for _ in stream:
                pass

            spans = span_exporter.get_finished_spans()
            assert len(spans) == 1

            chat_span = spans[0]
            assert chat_span.name == "openai.chat"

            # Check that the span has the GenAI entry attribute
            assert chat_span.attributes.get(GENAI_ENTRY_ATTRIBUTE) is True

        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value
            else:
                os.environ.pop(GENAI_ENTRY_ENV_VAR, None)

    @pytest.mark.vcr
    def test_embeddings_has_genai_entry_attribute(
        self, instrument_legacy, span_exporter, openai_client
    ):
        """Test that embeddings spans are marked with GenAI entry attribute."""
        original_value = os.environ.get(GENAI_ENTRY_ENV_VAR)
        try:
            os.environ[GENAI_ENTRY_ENV_VAR] = "true"

            openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input="Hello, embeddings test for GenAI entry"
            )

            spans = span_exporter.get_finished_spans()
            assert len(spans) == 1

            embedding_span = spans[0]
            assert embedding_span.name == "openai.embeddings"

            # Check that the span has the GenAI entry attribute
            assert embedding_span.attributes.get(GENAI_ENTRY_ATTRIBUTE) is True

        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value
            else:
                os.environ.pop(GENAI_ENTRY_ENV_VAR, None)

    @pytest.mark.vcr
    def test_completions_has_genai_entry_attribute(
        self, instrument_legacy, span_exporter, localhost_openai_client
    ):
        """Test that completions spans are marked with GenAI entry attribute."""
        original_value = os.environ.get(GENAI_ENTRY_ENV_VAR)
        try:
            os.environ[GENAI_ENTRY_ENV_VAR] = "true"

            localhost_openai_client.completions.create(
                model="gpt-3.5-turbo-instruct",
                prompt="Hello, completions test",
                max_tokens=10
            )

            spans = span_exporter.get_finished_spans()
            assert len(spans) == 1

            completion_span = spans[0]
            assert completion_span.name == "openai.completion"

            # Check that the span has the GenAI entry attribute
            assert completion_span.attributes.get(
                GENAI_ENTRY_ATTRIBUTE) is True

        finally:
            if original_value is not None:
                os.environ[GENAI_ENTRY_ENV_VAR] = original_value
            else:
                os.environ.pop(GENAI_ENTRY_ENV_VAR, None)

    def test_mark_span_as_genai_entry_function(self, span_exporter):
        """Test the mark_span_as_genai_entry function directly."""
        from opentelemetry import trace

        tracer = trace.get_tracer(__name__)
        with tracer.start_as_current_span("test_span") as span:
            # Test marking span as GenAI entry
            mark_span_as_genai_entry(span)

        spans = span_exporter.get_finished_spans()
        assert len(spans) == 1

        test_span = spans[0]
        assert test_span.attributes.get(GENAI_ENTRY_ATTRIBUTE) is True
