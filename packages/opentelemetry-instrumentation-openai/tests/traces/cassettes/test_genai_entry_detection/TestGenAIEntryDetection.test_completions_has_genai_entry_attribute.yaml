interactions:
- request:
    body: '{"model":"gpt-3.5-turbo-instruct","prompt":"Hello, completions test for
      GenAI entry","max_tokens":10}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate, br
      connection:
      - keep-alive
      content-length:
      - '101'
      content-type:
      - application/json
      host:
      - localhost:5002
      traceparent:
      - 00-eeb9b721efe8f39de229cd66cb44abe5-1f87b12f07cc39f8-01
      user-agent:
      - OpenAI/Python 1.59.7
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.59.7
      x-stainless-retry-count:
      - '0'
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.1
    method: POST
    uri: http://localhost:5002/v1/completions
  response:
    body:
      string: "{\"id\":\"cmpl-2nYZXNHxx1PeK1u8xXcE1Fqr1U6Ve\",\"object\":\"text_completion\",\"created\":1753729070,\"model\":\"gpt-3.5-turbo-instruct\",\"usage\":{\"prompt_tokens\":10,\"completion_tokens\":50,\"total_tokens\":60},\"choices\":[{\"text\":\"In
        the observability department, Prometheus, Grafana, Jaeger, and Loki were stressed.
        Prometheus groaned, \\\"12-hour backfills\u20148 a.m., 8 p.m.\u2014just to
        keep up.\\\" Grafana flipped her hair, \\\"47 dashboards, all screaming 'CRITICAL'\u2014users
        want real-time, not 'click here for yesterday's query.'\\\" Jaeger crossed
        arms, \\\"Agents breaking daily. A dev traced a Python function, sent telemetry
        to my email. Spent hours deleting 200 emails!\\\" Loki smirked, \\\"And logs?
        A mountain. We argue Jaeger vs. Zipkin vs. Tempo\u2014we forgot the goal:
        understanding what\u2019s really happening*.\\\"\\n\\n\",\"finish_reason\":\"stop\",\"logprobs\":null,\"index\":0}]}"
    headers:
      Connection:
      - close
      Content-Length:
      - '850'
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Mon, 28 Jul 2025 18:57:50 GMT
      Etag:
      - W/"352-v8EdwJG6gl7otlyVjnQ5EQhTPtE"
      X-Powered-By:
      - Express
    status:
      code: 200
      message: OK
version: 1
