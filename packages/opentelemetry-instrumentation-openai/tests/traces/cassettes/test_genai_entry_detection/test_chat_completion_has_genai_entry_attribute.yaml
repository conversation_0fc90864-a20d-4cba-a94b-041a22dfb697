interactions:
- request:
    body: '{"messages": [{"role": "user", "content": "Hello, test message for GenAI entry detection"}],
      "model": "gpt-3.5-turbo", "max_tokens": 10}'
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '125'
      content-type:
      - application/json
      host:
      - api.openai.com
      user-agent:
      - OpenAI/Python 1.12.0
      x-stainless-arch:
      - arm64
      x-stainless-async:
      - 'false'
      x-stainless-lang:
      - python
      x-stainless-os:
      - MacOS
      x-stainless-package-version:
      - 1.12.0
      x-stainless-runtime:
      - CPython
      x-stainless-runtime-version:
      - 3.12.1
    method: POST
    uri: https://api.openai.com/v1/chat/completions
  response:
    body:
      string: '{"id":"chatcmpl-test123","object":"chat.completion","created":1703123456,"model":"gpt-3.5-turbo-0613","choices":[{"index":0,"message":{"role":"assistant","content":"This is a test response for GenAI entry detection."},"logprobs":null,"finish_reason":"length"}],"usage":{"prompt_tokens":15,"completion_tokens":10,"total_tokens":25},"system_fingerprint":"fp_test123"}'
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 860b074e68f00e59-MXP
      Cache-Control:
      - no-cache, must-revalidate
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 21 Dec 2023 10:30:56 GMT
      Server:
      - cloudflare
      Set-Cookie:
      - __cf_bm=test_cookie; path=/; expires=Thu, 21-Dec-23 11:00:56 GMT; domain=.openai.com;
        HttpOnly; Secure; SameSite=None
      - _cfuvid=test_cookie2; path=/; domain=.openai.com; HttpOnly; Secure; SameSite=None
      Transfer-Encoding:
      - chunked
      access-control-allow-origin:
      - '*'
      alt-svc:
      - h3=":443"; ma=86400
      openai-model:
      - gpt-3.5-turbo-0613
      openai-organization:
      - org-test123
      openai-processing-ms:
      - '1234'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=15724800; includeSubDomains
      x-ratelimit-limit-requests:
      - '3500'
      x-ratelimit-limit-tokens:
      - '90000'
      x-ratelimit-remaining-requests:
      - '3499'
      x-ratelimit-remaining-tokens:
      - '89975'
      x-ratelimit-reset-requests:
      - 17ms
      x-ratelimit-reset-tokens:
      - 16ms
      x-request-id:
      - req-test123
    status:
      code: 200
      message: OK
version: 1
