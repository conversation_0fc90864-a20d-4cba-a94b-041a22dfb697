{"permissions": {"allow": ["Bash(ls:*)", "Bash(/Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -m pytest /Users/<USER>/Programming/oss/openllmetry/packages/opentelemetry-instrumentation-langchain/tests/test_genai_entry_detection.py -v)", "Bash(/Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -m pytest packages/opentelemetry-instrumentation-langchain/tests/test_genai_entry_detection.py -v)", "Bash(/Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -c \"import sys; print(sys.path)\")", "Bash(PYTHONPATH=\"/Users/<USER>/Programming/oss/openllmetry\" /Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -m pytest packages/opentelemetry-instrumentation-langchain/tests/test_genai_entry_detection.py::test_genai_entry_detection_on_chat_model_start -v -s)", "Bash(ANTHROPIC_API_KEY=\"test\" PYTHONPATH=\"/Users/<USER>/Programming/oss/openllmetry\" /Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -m pytest packages/opentelemetry-instrumentation-langchain/tests/test_genai_entry_detection.py::test_genai_entry_detection_on_chat_model_start -v -s)", "Bash(ANTHROPIC_API_KEY=\"test\" OPENAI_API_KEY=\"test\" PYTHONPATH=\"/Users/<USER>/Programming/oss/openllmetry\" /Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -m pytest packages/opentelemetry-instrumentation-langchain/tests/test_genai_entry_detection.py::test_genai_entry_detection_basic -v -s)", "Bash(ANTHROPIC_API_KEY=\"test\" OPENAI_API_KEY=\"test-key\" PYTHONPATH=\"/Users/<USER>/Programming/oss/openllmetry\" /Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -m pytest packages/opentelemetry-instrumentation-langchain/tests/test_genai_entry_detection.py::test_genai_entry_detection_on_chat_model_start -v -s)", "<PERSON><PERSON>(curl:*)", "Bash(PYTHONPATH=\"/Users/<USER>/Programming/oss/openllmetry\" /Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -c \"from langchain_openai import ChatOpenAI; help(ChatOpenAI.__init__)\")", "Bash(ANTHROPIC_API_KEY=\"test\" OPENAI_API_KEY=\"test-key\" PYTHONPATH=\"/Users/<USER>/Programming/oss/openllmetry\" /Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -m pytest packages/opentelemetry-instrumentation-langchain/tests/test_llms.py::test_openai -v -s)", "<PERSON><PERSON>(mkdir:*)", "Bash(ANTHROPIC_API_KEY=\"test\" OPENAI_API_KEY=\"test-key\" PYTHONPATH=\"/Users/<USER>/Programming/oss/openllmetry\" /Users/<USER>/Programming/oss/openllmetry/packages/sample-app/.venv/bin/python -m pytest packages/opentelemetry-instrumentation-langchain/tests/test_genai_entry_detection.py::test_genai_entry_detection_on_chat_model_start -v -s --vcr-record=new_episodes)", "Bash(rm:*)", "Bash(nx run:*)", "<PERSON><PERSON>(poetry run python:*)", "Bash(PYTHONPATH=\"/Users/<USER>/Programming/oss/openllmetry\" python3 -c \"from opentelemetry.semconv_ai.genai_entry import with_genai_entry_detection; print(''Import successful'')\")"], "deny": []}}