{"permissions": {"allow": ["<PERSON><PERSON>(python3:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(make test-traceloop:*)", "Bash(npx nx test:*)", "WebFetch(domain:github.com)", "WebFetch(domain:opentelemetry-python.readthedocs.io)", "Bash(python -m pytest tests/test_metrics_buckets.py -v)", "Bash(PYTHONPATH=/Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk:/Users/<USER>/Programming/oss/openllmetry/packages/opentelemetry-semantic-conventions-ai python3 -m pytest /Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk/tests/test_metrics_buckets.py::TestMetricsBuckets::test_llm_operation_duration_uses_default_buckets -v)", "Bash(PYTHONPATH=/Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk:/Users/<USER>/Programming/oss/openllmetry/packages/opentelemetry-semantic-conventions-ai python3 -c \"\nfrom traceloop.sdk.metrics.metrics import metric_views\nfrom opentelemetry.semconv_ai import Meters\n\nviews = metric_views()\nprint(''找到的视图数量:'', len(views))\n\nfor view in views:\n    if view.instrument_name == Meters.LLM_TOKEN_USAGE:\n        print(''LLM_TOKEN_USAGE 桶边界:'', view.aggregation._boundaries)\n    elif view.instrument_name == Meters.LLM_OPERATION_DURATION:\n        print(''LLM_OPERATION_DURATION 桶边界:'', view.aggregation._boundaries)\n\")", "<PERSON><PERSON>(poetry run:*)", "Bash(PYTHONPATH=/Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk:/Users/<USER>/Programming/oss/openllmetry/packages/opentelemetry-semantic-conventions-ai /Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk/.venv/bin/python -c \"\nfrom traceloop.sdk.metrics.metrics import metric_views\nfrom opentelemetry.semconv_ai import Meters\n\nviews = metric_views()\nprint(''找到的视图数量:'', len(views))\n\n# 查看所有视图的名称\nfor view in views:\n    print(f''视图: {view.instrument_name}'')\n    \n# 检查是否有LLM_OPERATION_DURATION的视图\nduration_views = [v for v in views if v.instrument_name == Meters.LLM_OPERATION_DURATION]\nprint(f''LLM_OPERATION_DURATION视图数量: {len(duration_views)}'')\n\")", "Bash(/Users/<USER>/.local/bin/poetry run python test_metrics_validation.py)", "Bash(WORKING_DIR=/Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk)", "Bash(PYTHONPATH=/Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk:/Users/<USER>/Programming/oss/openllmetry/packages/opentelemetry-semantic-conventions-ai)", "Bash(export PYTHONPATH)", "Bash(/Users/<USER>/.local/bin/poetry run --directory /Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk python test_metrics_validation.py)", "Bash(/Users/<USER>/.local/bin/poetry run --directory /Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk python test_metrics_simple.py)", "Bash(/Users/<USER>/.local/bin/poetry install --directory /Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk)", "Bash(/Users/<USER>/.local/bin/poetry run --directory /Users/<USER>/Programming/oss/openllmetry/packages/traceloop-sdk python test_actual_metrics.py)"], "deny": []}}