# Implementation Plan

## Task List

- [x] 1. 创建入口检测核心模块

  - 实现 GenAI 入口 span 检测逻辑
  - 创建配置管理模块
  - 实现安全的属性设置功能
  - _Requirements: 1.1, 1.2, 2.3, 3.1_

- [x] 1.1 实现入口检测逻辑

  - 创建 `genai_entry_detector.py` 模块
  - 实现 `is_entry_span()` 函数来检测根 span
  - 实现 `mark_entry_span()` 函数来设置属性
  - 添加异常处理和日志记录
  - _Requirements: 1.1, 1.2_

- [x] 1.2 创建配置管理模块

  - 创建 `genai_entry_config.py` 模块
  - 实现环境变量配置读取
  - 实现程序化配置支持
  - 添加配置验证逻辑
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 1.3 实现属性设置功能

  - 创建安全的属性设置函数
  - 添加错误处理和降级逻辑
  - 实现属性名称和值的常量定义
  - _Requirements: 1.4, 2.3_

- [x] 2. 集成入口检测到现有 wrapper 函数

  - 修改 chat completions wrapper
  - 修改 embeddings wrapper
  - 修改 completions wrapper
  - 修改 image generation wrapper
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 2.1 修改 chat completions wrapper

  - 在 `shared/chat_wrappers.py` 中集成入口检测
  - 修改 `chat_wrapper` 和 `achat_wrapper` 函数
  - 确保同步和异步版本都支持
  - 测试流式和非流式响应
  - _Requirements: 4.1_

- [x] 2.2 修改 embeddings wrapper

  - 在 `shared/embeddings_wrappers.py` 中集成入口检测
  - 修改 `embeddings_wrapper` 和 `aembeddings_wrapper` 函数
  - 确保同步和异步版本都支持
  - _Requirements: 4.2_

- [x] 2.3 修改 completions wrapper

  - 在 `shared/completion_wrappers.py` 中集成入口检测
  - 修改 `completion_wrapper` 和 `acompletion_wrapper` 函数
  - 确保同步和异步版本都支持
  - _Requirements: 4.4_

- [x] 2.4 修改 image generation wrapper

  - 在 `shared/image_gen_wrappers.py` 中集成入口检测
  - 修改 `image_gen_metrics_wrapper` 函数
  - _Requirements: 4.3_

- [x] 3. 支持 Beta APIs 和 Assistant APIs

  - 修改 assistant wrapper 函数
  - 修改 responses wrapper 函数
  - 确保所有 Beta API 都支持入口标记
  - _Requirements: 4.5_

- [x] 3.1 修改 assistant wrapper 函数

  - 在 `v1/assistant_wrappers.py` 中集成入口检测
  - 修改所有 assistant 相关的 wrapper 函数
  - 确保 Assistants API 支持入口标记
  - _Requirements: 4.5_

- [x] 3.2 修改 responses wrapper 函数

  - 在 `v1/responses_wrappers.py` 中集成入口检测
  - 修改所有 responses 相关的 wrapper 函数
  - 确保 Responses API 支持入口标记
  - _Requirements: 4.5_

- [x] 4. 添加配置选项到 OpenAIInstrumentor

  - 修改 OpenAIInstrumentor 构造函数
  - 添加 mark_genai_entry 参数
  - 更新配置传递逻辑
  - _Requirements: 3.3_

- [x] 4.1 修改 OpenAIInstrumentor 构造函数

  - 在 `__init__.py` 中添加 `mark_genai_entry` 参数
  - 更新 Config 类来存储配置
  - 确保配置正确传递到各个 wrapper
  - _Requirements: 3.3_

- [-] 5. 编写单元测试

  - 测试入口检测逻辑
  - 测试配置管理功能
  - 测试属性设置功能
  - 测试异常处理
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 5.1 测试入口检测逻辑

  - 创建测试文件 `test_genai_entry_detector.py`
  - 测试根 span 检测
  - 测试子 span 检测
  - 测试边界情况
  - _Requirements: 1.1, 1.2_

- [ ] 5.2 测试配置管理功能

  - 创建测试文件 `test_genai_entry_config.py`
  - 测试环境变量配置
  - 测试程序化配置
  - 测试配置验证
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 5.3 测试属性设置功能

  - 测试属性正确设置
  - 测试异常处理
  - 测试性能影响
  - _Requirements: 1.4, 2.3_

- [ ] 6. 编写集成测试

  - 测试完整的 API 调用流程
  - 测试多层嵌套调用场景
  - 测试并发场景
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 6.1 测试完整 API 调用流程

  - 创建集成测试文件 `test_genai_entry_integration.py`
  - 测试所有支持的 OpenAI API
  - 验证入口标记的正确性
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 6.2 测试嵌套调用场景

  - 测试在已有 span 内调用 OpenAI API
  - 验证只有真正的入口 span 被标记
  - 测试复杂的调用链
  - _Requirements: 1.3_

- [ ] 7. 更新文档和示例

  - 更新 README 文档
  - 添加配置说明
  - 创建使用示例
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 7.1 更新文档
  - 在 README 中添加入口标记功能说明
  - 添加配置选项文档
  - 添加使用示例和最佳实践
  - _Requirements: 3.1, 3.2, 3.3_
