# Design Document - GenAI Entry Span Marker (高效版本)

## Overview

本设计文档描述了如何在 OpenAI instrumentation 中高效实现 GenAI 入口 span 标记功能。核心思路是使用 OpenTelemetry Context Variables 来跟踪 GenAI 操作状态，避免昂贵的 span 链遍历操作。

## 核心设计原则

1. **高效性**: 使用 Context Variables，O(1) 复杂度检查
2. **准确性**: 正确识别 GenAI 操作链的入口点
3. **兼容性**: 不影响现有功能和性能
4. **简洁性**: 最小化代码修改和复杂度

## 高效实现方案

### 核心思路

使用 OpenTelemetry Context Variables 来跟踪当前是否已经在 GenAI 操作中：

```python
# 定义 context key
GENAI_OPERATION_ACTIVE_KEY = "genai_operation_active"

def is_genai_entry_span() -> bool:
    """
    高效检查是否为 GenAI 入口 span

    时间复杂度: O(1)
    空间复杂度: O(1)
    """
    # 检查当前 context 中是否已经有 GenAI 操作标记
    return not context_api.get_value(GENAI_OPERATION_ACTIVE_KEY, False)

def mark_genai_operation_active():
    """设置 GenAI 操作为活跃状态"""
    return context_api.set_value(GENAI_OPERATION_ACTIVE_KEY, True)
```

### 集成方案

修改现有的 wrapper 函数，在创建 span 时：

1. 检查是否为入口 span
2. 如果是入口，设置 context variable 和 span 属性
3. Context variable 会在 span 结束时自动清除

```python
def enhanced_chat_wrapper(tracer, *args, **kwargs):
    """增强的 chat wrapper，支持入口标记"""

    # 1. 检查是否为入口 span（O(1) 操作）
    is_entry = is_genai_entry_span()

    # 2. 设置 GenAI 操作活跃状态
    if is_entry:
        genai_context = mark_genai_operation_active()
        context_token = context_api.attach(genai_context)
    else:
        context_token = None

    try:
        # 3. 创建 span
        span = tracer.start_span(
            SPAN_NAME,
            kind=SpanKind.CLIENT,
            attributes={SpanAttributes.LLM_REQUEST_TYPE: LLM_REQUEST_TYPE.value},
        )

        # 4. 如果是入口且功能启用，添加标记
        if is_entry and GenAIEntryConfig.is_enabled():
            span.set_attribute("is_genai_entry", True)

        # 5. 执行原有逻辑
        # ... 现有的 wrapper 逻辑 ...

    finally:
        # 6. 清理 context（如果设置了的话）
        if context_token is not None:
            context_api.detach(context_token)
```

## 实现细节

### 1. Context Variable 管理

```python
from opentelemetry import context as context_api

# Context key 定义
GENAI_OPERATION_ACTIVE_KEY = "genai_operation_active"

class GenAIContextManager:
    """GenAI Context 管理器"""

    @staticmethod
    def is_genai_active() -> bool:
        """检查当前是否在 GenAI 操作中"""
        return context_api.get_value(GENAI_OPERATION_ACTIVE_KEY, False)

    @staticmethod
    def set_genai_active():
        """设置 GenAI 操作为活跃状态"""
        return context_api.set_value(GENAI_OPERATION_ACTIVE_KEY, True)

    @staticmethod
    def is_entry_span() -> bool:
        """检查是否为入口 span"""
        return not GenAIContextManager.is_genai_active()
```

### 2. 配置管理

```python
import os

class GenAIEntryConfig:
    """GenAI 入口标记配置"""

    @staticmethod
    def is_enabled() -> bool:
        """检查是否启用入口标记功能"""
        return os.getenv("OPENAI_MARK_GENAI_ENTRY", "false").lower() == "true"

    @staticmethod
    def get_attribute_name() -> str:
        """获取属性名称"""
        return "is_genai_entry"
```

### 3. 通用 Wrapper 增强函数

```python
def enhance_wrapper_with_entry_detection(original_wrapper):
    """
    装饰器：为现有 wrapper 添加入口检测功能

    这个装饰器可以应用到所有现有的 wrapper 函数上，
    最小化代码修改
    """
    def enhanced_wrapper(tracer, *wrapper_args, **wrapper_kwargs):
        def inner_wrapper(wrapped, instance, args, kwargs):
            # 检查是否为入口 span
            is_entry = GenAIContextManager.is_entry_span()

            # 设置 GenAI context
            context_token = None
            if is_entry:
                genai_context = GenAIContextManager.set_genai_active()
                context_token = context_api.attach(genai_context)

            try:
                # 调用原始 wrapper
                result = original_wrapper(tracer, *wrapper_args, **wrapper_kwargs)(
                    wrapped, instance, args, kwargs
                )

                # 如果是入口且功能启用，在 span 上添加标记
                if is_entry and GenAIEntryConfig.is_enabled():
                    current_span = trace.get_current_span()
                    if current_span != trace.INVALID_SPAN:
                        current_span.set_attribute(
                            GenAIEntryConfig.get_attribute_name(),
                            True
                        )

                return result

            finally:
                # 清理 context
                if context_token is not None:
                    context_api.detach(context_token)

        return inner_wrapper
    return enhanced_wrapper
```

## 修改点分析

### 需要修改的文件

1. **新增文件**:

   - `shared/genai_entry_detector.py` - 核心检测逻辑
   - `shared/genai_entry_config.py` - 配置管理

2. **修改文件**:
   - `shared/chat_wrappers.py` - 添加入口检测
   - `shared/embeddings_wrappers.py` - 添加入口检测
   - `shared/completion_wrappers.py` - 添加入口检测
   - `shared/image_gen_wrappers.py` - 添加入口检测
   - `v1/assistant_wrappers.py` - 添加入口检测
   - `v1/responses_wrappers.py` - 添加入口检测
   - `__init__.py` - 添加配置选项

### 修改策略

使用装饰器模式，最小化对现有代码的修改：

```python
# 在每个 wrapper 文件中，只需要添加一行装饰器
@enhance_wrapper_with_entry_detection
def chat_wrapper(tracer, *args, **kwargs):
    # 现有代码保持不变
    pass
```

## 性能分析

### 时间复杂度

- **入口检测**: O(1) - 只需要读取 context variable
- **Context 设置**: O(1) - 设置 context variable
- **总体影响**: 几乎可以忽略不计

### 空间复杂度

- **Context Variable**: O(1) - 每个 context 只存储一个 boolean
- **总体影响**: 最小

### 与遍历方案对比

- **遍历方案**: O(n) 时间复杂度，n 为 span 链长度
- **Context 方案**: O(1) 时间复杂度
- **性能提升**: 显著，特别是在深层调用链中

## 测试策略

### 核心测试场景

```python
def test_context_based_entry_detection():
    """测试基于 context 的入口检测"""

    # 场景1: 独立调用（应该是入口）
    assert GenAIContextManager.is_entry_span() == True

    # 场景2: 在 GenAI context 中（不应该是入口）
    genai_context = GenAIContextManager.set_genai_active()
    with context_api.attach(genai_context):
        assert GenAIContextManager.is_entry_span() == False

    # 场景3: 嵌套的非 GenAI span 中（应该是入口）
    with tracer.start_as_current_span("http_request"):
        assert GenAIContextManager.is_entry_span() == True

def test_context_cleanup():
    """测试 context 自动清理"""
    # 确保 context 在 span 结束后正确清理
    pass

def test_concurrent_operations():
    """测试并发场景下的正确性"""
    # 确保不同线程/协程的 context 不会互相影响
    pass
```

## 优势总结

1. **高性能**: O(1) 复杂度，无需遍历 span 链
2. **准确性**: 正确识别 GenAI 操作链的入口
3. **简洁性**: 最小化代码修改，使用装饰器模式
4. **可靠性**: 利用 OpenTelemetry 的 context 机制，自动清理
5. **可扩展性**: 易于扩展到其他 GenAI 相关的插桩包
