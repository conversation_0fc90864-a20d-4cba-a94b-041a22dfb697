# Requirements Document

## Introduction

本需求旨在为 OpenAI instrumentation 的入口 span 添加一个标记属性 `is_genai_entry`，用于标识这是一个 GenAI 操作的入口点。这个标记将帮助下游系统识别和处理 GenAI 相关的链路追踪数据。

## Requirements

### Requirement 1

**User Story:** 作为一个可观测性系统的开发者，我希望能够识别哪些 span 是 GenAI 操作的入口点，以便进行特殊的处理和分析。

#### Acceptance Criteria

1. WHEN OpenAI API 调用被插桩时 THEN 系统应该检测当前 span 链中是否已经存在其他 GenAI 相关的 span
2. WHEN 检测到这是 GenAI 操作链中的第一个 span 时 THEN 系统应该添加 `is_genai_entry=True` 属性
3. WHEN 当前调用已经在 GenAI span 内部时 THEN 系统不应该添加 `is_genai_entry` 属性
4. WHEN 添加了 `is_genai_entry` 属性时 THEN 该属性的类型必须是 boolean，值为 True

### Requirement 2

**User Story:** 作为一个应用开发者，我希望这个标记功能不会影响现有的 OpenAI instrumentation 行为，保持向后兼容性。

#### Acceptance Criteria

1. WHEN 启用入口标记功能时 THEN 现有的所有 span 属性和 metrics 收集不应受到影响
2. WHEN 禁用入口标记功能时 THEN 系统应该正常工作，不添加 `is_genai_entry` 属性
3. WHEN 系统检测父 span 失败时 THEN 应该优雅降级，不影响正常的插桩功能

### Requirement 3

**User Story:** 作为一个系统管理员，我希望能够通过配置控制是否启用入口标记功能。

#### Acceptance Criteria

1. WHEN 设置环境变量 `OPENAI_MARK_GENAI_ENTRY=true` 时 THEN 系统应该启用入口标记功能
2. WHEN 环境变量未设置或设置为 `false` 时 THEN 系统应该禁用入口标记功能
3. WHEN 通过程序化配置启用功能时 THEN 系统应该支持在 OpenAIInstrumentor 初始化时传入参数

### Requirement 4

**User Story:** 作为一个调试人员，我希望能够在所有支持的 OpenAI API 类型上看到入口标记。

#### Acceptance Criteria

1. WHEN 调用 Chat Completions API 时 THEN 入口 span 应该包含 `is_genai_entry=True`
2. WHEN 调用 Embeddings API 时 THEN 入口 span 应该包含 `is_genai_entry=True`
3. WHEN 调用 Image Generation API 时 THEN 入口 span 应该包含 `is_genai_entry=True`
4. WHEN 调用 Text Completions API 时 THEN 入口 span 应该包含 `is_genai_entry=True`
5. WHEN 调用 Beta APIs（Assistants 等）时 THEN 入口 span 应该包含 `is_genai_entry=True`
