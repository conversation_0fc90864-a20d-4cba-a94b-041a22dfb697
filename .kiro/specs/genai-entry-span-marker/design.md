# Design Document

## Overview

本设计文档描述了如何在 OpenAI instrumentation 中实现 GenAI 入口 span 标记功能。该功能通过检测当前 span 是否为根 span 来判断是否为 GenAI 操作的入口点，并相应地添加 `is_genai_entry` 属性。

## Architecture

### 核心组件

1. **Entry Detection Module**: 负责检测当前 span 是否为入口 span
2. **Configuration Module**: 管理功能开关和配置选项
3. **Span Attribute Setter**: 负责设置 `is_genai_entry` 属性
4. **Integration Points**: 在现有的 wrapper 函数中集成入口检测逻辑

### 检测逻辑

```python
def is_genai_entry_span() -> bool:
    """
    检测当前是否为 GenAI 操作的入口 span

    在 GenAI 插桩探针项目中，正确的判断逻辑应该是：
    1. 检查当前 span 链中是否已经存在其他 GenAI 相关的 span
    2. 如果不存在，则当前 OpenAI API 调用就是 GenAI 操作的入口点
    3. 即使存在非 GenAI 的父 span（如 HTTP、数据库等），当前调用仍可能是 GenAI 入口

    关键洞察：入口不是指"根 span"，而是指"GenAI 操作链的第一个 span"
    """
    # 如果当前没有活跃的 span，这肯定是入口
    current_span = trace.get_current_span()
    if current_span is trace.INVALID_SPAN:
        return True

    # 检查当前 span 链中是否已经存在 GenAI 相关的 span
    return not _has_genai_span_in_current_context()

def _has_genai_span_in_current_context() -> bool:
    """
    检查当前 context 中是否已经存在 GenAI 相关的 span

    实现策略：
    1. 使用 context variables 来跟踪 GenAI 操作状态
    2. 或者检查当前 span 的属性来判断是否已经在 GenAI 操作中
    3. 由于无法直接遍历父 span 链，使用间接方法
    """
    current_span = trace.get_current_span()
    if current_span is trace.INVALID_SPAN:
        return False

    # 方案1: 检查当前 span 是否已经有 GenAI 属性
    # 这种情况下，说明我们已经在一个 GenAI span 内部
    try:
        # 尝试获取 span 属性（不同 OpenTelemetry 版本可能不同）
        if hasattr(current_span, 'attributes'):
            attributes = current_span.attributes
        elif hasattr(current_span, '_attributes'):
            attributes = current_span._attributes
        else:
            attributes = {}

        # 检查是否有 GenAI 相关的属性
        genai_indicators = [
            'gen_ai.system',
            'llm.request.type',
            'is_genai_entry',
            SpanAttributes.LLM_SYSTEM if 'SpanAttributes' in globals() else 'gen_ai.system'
        ]

        for indicator in genai_indicators:
            if indicator in attributes:
                return True

    except Exception:
        # 如果获取属性失败，采用保守策略
        pass

    # 方案2: 使用 context variable 来跟踪（更可靠的方法）
    # 这需要在进入 GenAI span 时设置标记，退出时清除
    return context_api.get_value("genai_operation_active", False)
```

## Components and Interfaces

### 1. Configuration Interface

```python
class GenAIEntryConfig:
    """GenAI 入口标记配置"""

    @staticmethod
    def is_enabled() -> bool:
        """检查是否启用入口标记功能"""
        return os.getenv("OPENAI_MARK_GENAI_ENTRY", "false").lower() == "true"

    @staticmethod
    def get_attribute_name() -> str:
        """获取属性名称"""
        return "is_genai_entry"
```

### 2. Entry Detection Interface

```python
class GenAIEntryDetector:
    """GenAI 入口检测器"""

    @staticmethod
    def is_entry_span(context: Optional[Context] = None) -> bool:
        """
        检测是否为入口 span

        Args:
            context: OpenTelemetry context，如果为 None 则使用当前 context

        Returns:
            bool: 如果是入口 span 返回 True，否则返回 False
        """
        pass

    @staticmethod
    def mark_entry_span(span: Span) -> None:
        """
        标记 span 为入口 span

        Args:
            span: 要标记的 span
        """
        pass
```

### 3. Integration Points

需要在以下 wrapper 函数中集成入口检测逻辑：

1. **Chat Completions**: `chat_wrapper`, `achat_wrapper`
2. **Text Completions**: `completion_wrapper`, `acompletion_wrapper`
3. **Embeddings**: `embeddings_wrapper`, `aembeddings_wrapper`
4. **Image Generation**: `image_gen_metrics_wrapper`
5. **Assistant APIs**: 各种 assistant wrapper 函数

### 4. 修改点分析

基于代码分析，主要修改点：

```python
# 在 shared/chat_wrappers.py 中
def chat_wrapper(...):
    span = tracer.start_span(
        SPAN_NAME,
        kind=SpanKind.CLIENT,
        attributes={SpanAttributes.LLM_REQUEST_TYPE: LLM_REQUEST_TYPE.value},
    )

    # 添加入口检测逻辑
    if GenAIEntryConfig.is_enabled():
        if GenAIEntryDetector.is_entry_span():
            GenAIEntryDetector.mark_entry_span(span)
```

## Data Models

### Span Attribute Schema

```python
GENAI_ENTRY_ATTRIBUTE = "is_genai_entry"
GENAI_ENTRY_VALUE = True  # 只有入口 span 才会有这个属性，值固定为 True
```

### Configuration Schema

```python
# 环境变量配置
OPENAI_MARK_GENAI_ENTRY = "true" | "false"  # 默认 "false"

# 程序化配置
OpenAIInstrumentor(mark_genai_entry=True)
```

## Error Handling

### 异常处理策略

1. **检测失败**: 如果入口检测失败，应该优雅降级，不影响正常插桩
2. **属性设置失败**: 如果设置属性失败，记录警告但不中断执行
3. **配置错误**: 如果配置无效，使用默认值（禁用功能）

```python
def safe_mark_entry_span(span: Span) -> None:
    """安全地标记入口 span"""
    try:
        if GenAIEntryConfig.is_enabled():
            if GenAIEntryDetector.is_entry_span():
                span.set_attribute(GENAI_ENTRY_ATTRIBUTE, True)
    except Exception as e:
        logger.warning(f"Failed to mark GenAI entry span: {e}")
        # 不重新抛出异常，确保不影响正常功能
```

## Testing Strategy

### 单元测试

1. **入口检测测试**: 测试各种场景下的入口检测逻辑
2. **配置测试**: 测试配置开关的正确性
3. **属性设置测试**: 测试属性设置的正确性
4. **异常处理测试**: 测试异常情况下的优雅降级

### 集成测试

1. **端到端测试**: 测试完整的 OpenAI API 调用流程
2. **多层调用测试**: 测试嵌套调用场景下的入口检测
3. **并发测试**: 测试并发场景下的正确性

### 测试场景

```python
# 场景1: 独立的 GenAI 调用（应该标记为入口）
def test_standalone_genai_call_marked_as_entry():
    # 直接调用 OpenAI API，没有任何 span context
    # 预期：is_genai_entry=True
    pass

# 场景2: 在非 GenAI span 内的 GenAI 调用（应该标记为入口）
def test_genai_call_in_non_genai_span_marked_as_entry():
    # 在 HTTP request span 内调用 OpenAI API
    # 预期：is_genai_entry=True（因为这是 GenAI 操作链的开始）
    with tracer.start_as_current_span("http_request"):
        # OpenAI API 调用应该被标记为 GenAI 入口
        pass

# 场景3: 在已有 GenAI span 内的调用（不应该标记为入口）
def test_genai_call_in_existing_genai_span_not_marked():
    # 在已有的 GenAI span 内再次调用 OpenAI API
    # 预期：不应该添加 is_genai_entry 属性
    with tracer.start_as_current_span("openai.chat", attributes={"gen_ai.system": "openai"}):
        # 嵌套的 OpenAI 调用不应该被标记为入口
        pass

# 场景4: 功能禁用（不应该添加属性）
def test_feature_disabled():
    # 设置环境变量禁用功能
    # 预期：即使是入口调用也不应该添加属性
    pass

# 场景5: 复杂调用链中的 GenAI 入口
def test_complex_call_chain_genai_entry():
    # HTTP -> Database -> GenAI 调用链
    # 预期：GenAI 调用应该被标记为入口
    with tracer.start_as_current_span("http_handler"):
        with tracer.start_as_current_span("db_query"):
            # 这里的 OpenAI 调用应该是 GenAI 入口
            pass

# 场景6: 多个独立的 GenAI 调用
def test_multiple_independent_genai_calls():
    # 同一个 trace 中的多个独立 GenAI 调用
    # 预期：每个都应该被标记为入口（如果它们不在彼此的 span 内）
    pass
```

## Implementation Considerations

### 技术挑战

1. **父 span 检测**: OpenTelemetry 不直接暴露父 span 信息，需要通过 context 推断
2. **性能影响**: 入口检测逻辑应该尽可能轻量，避免影响性能
3. **兼容性**: 确保与现有代码和未来版本的兼容性

### 解决方案

1. **Context 分析**: 通过分析当前 context 中的 span 信息来判断是否为根 span
2. **缓存优化**: 对检测结果进行适当缓存，避免重复计算
3. **版本兼容**: 使用条件导入和特性检测确保兼容性

### 实现优先级

1. **Phase 1**: 实现基础的入口检测和标记功能
2. **Phase 2**: 添加配置选项和错误处理
3. **Phase 3**: 优化性能和添加高级功能
